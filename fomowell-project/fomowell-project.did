type Account = record { owner : principal; subaccount : opt vec nat8 };
type Comments = record {
  user_star : vec principal;
  user_pid : principal;
  content : text;
  image_url : opt text;
  fomo_idx : text;
  create_time : nat64;
  comment_idx : nat64;
  extended : vec record { text; text };
};
type CommentsCreate = record {
  content : text;
  image_url : opt text;
  extended : vec record { text; text };
};
type CommentsVo = record {
  start_idx : nat64;
  end_idx : nat64;
  fomo_vec : vec Comments;
};
type HolderInfo = record {
  holder_type : text;
  account : Account;
  amount : nat;
  holder_percent : float64;
};
type Page = record { limit : nat64; start : nat64 };
type Result = variant { Ok; Err : text };
service : (vec record { text; text }) -> {
  add_comment : (CommentsCreate) -> (Result);
  cycles : () -> (nat64) query;
  get_comments_by_index : (Page) -> (CommentsVo) query;
  get_comments_len : () -> (nat) query;
  get_fomo_info : () -> (vec record { text; text }) query;
  get_top10_holders : () -> (vec HolderInfo) query;
}