pub const ICP_LEDGER_CANISTER: &str = "ryjl3-tyaaa-aaaaa-aaaba-cai";
pub const ICP_LEDGER_FEE: u128 = 10_000u128;

//icp swap
pub const ICP_SWAP_POOL_FEE: u128 = 3_000;
//prod env
pub const ICP_SWAP_CREATION_FEES: u128 = 100_000_000; //1 icp
pub const ICP_SWAP_PASSCODEMANAGER: &str = "7eikv-2iaaa-aaaag-qdgwa-cai";
pub const ICP_SWAP_FACTORY: &str = "4mmnk-kiaaa-aaaag-qbllq-cai";
pub const ICP_SWAP_CALCULATOR: &str = "phr2m-oyaaa-aaaag-qjuoq-cai";


//test env
pub const ICP_SWAP_CREATION_FEES_TEST: u128 = 10_000_000; //0.1 icp for test
pub const ICP_SWAP_PASSCODEMANAGER_TEST: &str = "pybqd-4yaaa-aaaag-ak5ta-cai";
pub const ICP_SWAP_FACTORY_TEST: &str = "ososz-6iaaa-aaaag-ak5ua-cai";
pub const ICP_SWAP_CALCULATOR_TEST: &str = "phr2m-oyaaa-aaaag-qjuoq-cai";
