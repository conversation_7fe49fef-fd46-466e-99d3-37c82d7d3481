use candid::{<PERSON>, Principal};
use ic_cdk::{api::time, id};
use icrc_ledger_types::icrc1::{account::Account, transfer::TransferArg};

use crate::{
    constants::{ICP_LEDGER_CANISTER, ICP_LEDGER_FEE},
    icp_swap_calculator_canister::ICPSwapCalculator,
    icp_swap_factory_canister::ICPSwapFactory,
    icp_swap_passcode_manager_canister::ICPSwapPasscodeManager,
    icp_swap_pool_canister::ICPSwapPool,
    icrc1::{get_sub_account_from_principal, icrc1_transfer},
    types::{Env, ICPSwapConfig, LaunchRequestArg, MintArgs},
};

pub async fn launch(env: Env, launch_arg: LaunchRequestArg) -> Result<Principal, String> {
    // get icp_swap_config
    let (icp_swap_calculator, icp_swap_factory, icp_swap_passcode_manager, icp_swap_creation_fee) =
        get_icp_swap_setting(env);

    //1.create icpspwa pool
    //1.1 transfer creation_fee from the caller to PasscodeManager,Use PasscodeManager.requestPasscode to request a passcode for creating a SwapPool.
    icrc1_transfer(
        Principal::from_text(ICP_LEDGER_CANISTER).unwrap(),
        TransferArg {
            from_subaccount: launch_arg.subaccount,
            to: Account {
                owner: icp_swap_passcode_manager.0,
                subaccount: get_sub_account_from_principal(id()),
            },
            fee: Some(Nat::from(ICP_LEDGER_FEE)),
            created_at_time: Some(time()),
            memo: None,
            amount: icp_swap_creation_fee.clone() + 5 * ICP_LEDGER_FEE, // icp swap support send 5*fee
        },
    )
    .await?;
    icp_swap_passcode_manager
        .deposit_creatiton_fee(icp_swap_creation_fee.clone())
        .await?;
    //1.2 sort token0 token1
    let (token0, token1) = if launch_arg.token1.token.address > launch_arg.token0.token.address {
        (launch_arg.token0, launch_arg.token1)
    } else {
        (launch_arg.token1, launch_arg.token0)
    };
    //1.3 PasscodeManager.requestPasscode to request a passcode for creating a SwapPool.
    icp_swap_passcode_manager
        .reqeust_passcode(token0.token.clone(), token1.token.clone())
        .await?;

    //1.4 getSqrtPriceX96
    let price = token1.balance as f64 / token0.balance as f64;
    let sqrt_price_x96 = icp_swap_calculator
        .get_sqrt_price_x96(price, token0.decimal, token1.decimal)
        .await?;
    //1.5 create pool
    let pool_canister = icp_swap_factory
        .create_pool(
            token0.token.clone(),
            token1.token.clone(),
            sqrt_price_x96.to_string(),
        )
        .await?;
    let icp_swap_pool = ICPSwapPool::new(pool_canister);

    //2.mint liqidity
    //2.1 transfer token0
    icrc1_transfer(
        Principal::from_text(token0.token.address.clone()).unwrap(),
        TransferArg {
            from_subaccount: launch_arg.subaccount,
            to: Account {
                owner: pool_canister,
                subaccount: get_sub_account_from_principal(id()),
            },
            fee: Some(Nat::from(token0.fee)),
            created_at_time: Some(time()),
            memo: None,
            amount: Nat::from(token0.balance - token0.fee),
        },
    )
    .await?;
    icp_swap_pool
        .deposit(
            token0.token.clone(),
            token0.fee,
            token0.balance - token0.fee,
        )
        .await?;
    //2.2 transfer token1
    icrc1_transfer(
        Principal::from_text(token1.token.address.clone()).unwrap(),
        TransferArg {
            from_subaccount: launch_arg.subaccount,
            to: Account {
                owner: pool_canister,
                subaccount: get_sub_account_from_principal(id()),
            },
            fee: Some(Nat::from(token1.fee)),
            created_at_time: Some(time()),
            memo: None,
            amount: Nat::from(token1.balance - token1.fee),
        },
    )
    .await?;
    icp_swap_pool
        .deposit(
            token1.token.clone(),
            token1.fee,
            token1.balance - token1.fee,
        )
        .await?;
    //2.3 pool_canister metadata
    let metadata = icp_swap_pool.metadata().await?;
    //2.4 tick
    let args: (Nat, Nat, Nat) = (
        metadata.sqrt_price_x96.clone(),
        Nat::from(token0.decimal),
        Nat::from(token1.decimal),
    );
    let price = icp_swap_calculator.get_price(args).await?;
    let price_lower = price / 10.0;
    let tick_lower = icp_swap_calculator.get_tick(price_lower).await?;
    let price_upper = price * 10.0;
    let tick_upper = icp_swap_calculator.get_tick(price_upper).await?;
    //2.5 swap_calculator getPositionTokenAmount
    let args = (
        metadata.sqrt_price_x96,
        metadata.tick,
        tick_lower.clone(),
        tick_upper.clone(),
        token0.balance - 5 * token0.fee, //icp swap support send 5*fee
        token1.balance - 5 * token1.fee, //icp swap support send 5*fee
    );
    let position = icp_swap_calculator.get_position_token_amount(args).await?;
    //2.6 pool_canister mint liquidity position
    let args = MintArgs {
        fee: metadata.fee,
        tick_upper: tick_upper,
        tick_lower: tick_lower,
        token0: metadata.token0.address,
        token1: metadata.token1.address,
        amount0_desired: position.amount0.to_string(),
        amount1_desired: position.amount1.to_string(),
    };
    icp_swap_pool.mint_liqudity(args).await?;
    Ok(pool_canister)
}

fn get_icp_swap_setting(
    env: Env,
) -> (
    ICPSwapCalculator,
    ICPSwapFactory,
    ICPSwapPasscodeManager,
    Nat,
) {
    let config = ICPSwapConfig::new(env);
    (
        ICPSwapCalculator::new(config.icp_swap_calculator_canister),
        ICPSwapFactory::new(config.icp_swap_factory_canister),
        ICPSwapPasscodeManager::new(config.icp_swap_passcode_manager_canister),
        Nat::from(config.icp_swap_creation_fee),
    )
}
