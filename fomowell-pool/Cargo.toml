[package]
name = "fomowell_pool"
version = "0.1.0"
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[dependencies]
ic-cdk-macros = "0.7"
candid = "0.9"
serde = "1.0"
ic-cdk = "0.10.1"
assert-panic = "1.0.1"
icpex_lib = { path = '../fomowell-icpex-lib', version = '0.1.0' }

[features]
local = []
test = []
pro = []

[target.'cfg(not(target_family = "wasm"))'.dependencies]
async-std = { version = "1.10.0", features = ["attributes"] }
