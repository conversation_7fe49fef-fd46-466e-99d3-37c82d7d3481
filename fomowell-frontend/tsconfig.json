{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "lib": ["DOM", "DOM.Iterable", "ESNext"], "allowJs": false, "skipLibCheck": true, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "forceConsistentCasingInFileNames": true, "module": "ESNext", "moduleResolution": "Node", "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "jsx": "react-jsx", "baseUrl": "./src", "paths": {"@/*": ["./*"]}}, "include": ["src"], "exclude": ["node_modules", "**/node_modules/*", "dist", "**/dist/*"], "references": [{"path": "./tsconfig.node.json"}], "ts-node": {"compilerOptions": {"types": ["node"]}}}