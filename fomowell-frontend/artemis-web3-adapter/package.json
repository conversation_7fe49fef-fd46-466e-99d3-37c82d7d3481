{"name": "artemis-web3-adapter", "version": "0.4.9", "description": "dfinity wallet adapter for connecting different wallets in IC", "main": "index.js", "scripts": {"build": "webpack --mode production"}, "author": "code_lion", "license": "ISC", "dependencies": {"@astrox/sdk-web": "^0.1.41", "@astrox/sdk-webview": "^0.1.41", "@dfinity/agent": "^1.3.0", "@dfinity/auth-client": "^1.2.1", "@dfinity/authentication": "^0.14.2", "@dfinity/candid": "^1.3.0", "@dfinity/identity": "^1.2.1", "@dfinity/principal": "0.12.2", "@dfinity/utils": "^2.2.0", "@fort-major/msq-client": "^0.3.4", "@fort-major/msq-shared": "^0.3.3", "buffer": "^6.0.3", "buffer-crc32": "^0.2.13", "crypto-js": "^4.1.1", "ic-stoic-identity": "^6.0.0"}, "devDependencies": {"webpack": "^5.75.0", "webpack-cli": "^5.0.1"}, "engines": {"npm": ">=7.0.0", "node": ">=14.0.0"}}