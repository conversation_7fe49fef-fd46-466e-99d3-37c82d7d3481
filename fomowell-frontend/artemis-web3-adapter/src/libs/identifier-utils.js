import { Principal } from '@dfinity/principal';
import crc32 from 'buffer-crc32';
import { <PERSON><PERSON><PERSON> } from 'buffer';
import crypto from "crypto-js";


// @ts-ignore
window.Buffer = Buffer;
const SUB_ACCOUNT_ZERO = Buffer.alloc(32);
const ACCOUNT_DOMAIN_SEPERATOR = '\x0Aaccount-id';

const to32bits = (num) => {
  const b = new ArrayBuffer(4);
  new DataView(b).setUint32(0, num);
  return Array.from(new Uint8Array(b));
};

const getTokenIdentifier = (canisterId, tokenIndex) => {
  const padding = Buffer.from('\x0Atid');
  const array = new Uint8Array([
    ...padding,
    ...Principal.fromText(canisterId).toUint8Array(),
    ...to32bits(tokenIndex),
  ]);
  return Principal.fromUint8Array(array).toText();
};

const intToHex = (val) =>
  val < 0 ? (Number(val) >>> 0).toString(16) : Number(val).toString(16);

// We generate a CRC32 checksum, and trnasform it into a hexString
const generateChecksum = (hash) => {
  const crc = crc32.unsigned(Buffer.from(hash));
  const hex = intToHex(crc);
  return hex.padStart(8, '0');
};

const byteArrayToWordArray = (byteArray) => {
  const wordArray = [];
  let i;
  for (i = 0; i < byteArray.length; i += 1) {
    wordArray[(i / 4) | 0] |= byteArray[i] << (24 - 8 * i);
  }
  // eslint-disable-next-line
  const result = crypto.lib.WordArray.create(wordArray, byteArray.length);
  return result;
};

const wordToByteArray = (word, length) => {
  const byteArray = [];
  const xFF = 0xff;
  if (length > 0) byteArray.push(word >>> 24);
  if (length > 1) byteArray.push((word >>> 16) & xFF);
  if (length > 2) byteArray.push((word >>> 8) & xFF);
  if (length > 3) byteArray.push(word & xFF);

  return byteArray;
};

const wordArrayToByteArray = (wordArray, length) => {
  if (
    wordArray.hasOwnProperty('sigBytes') &&
    wordArray.hasOwnProperty('words')
  ) {
    length = wordArray.sigBytes;
    wordArray = wordArray.words;
  }

  let result = [];
  let bytes;
  let i = 0;
  while (length > 0) {
    bytes = wordToByteArray(wordArray[i], Math.min(4, length));
    length -= bytes.length;
    result = [...result, bytes];
    i++;
  }
  return [].concat.apply([], result);
};

const getAccountIdentifier = (principalId, subAccount='') => {
  try {
    var principal = Principal.from(principalId);
    const sha = crypto.algo.SHA224.create();
    sha.update(ACCOUNT_DOMAIN_SEPERATOR); // Internally parsed with UTF-8, like go does
    sha.update(byteArrayToWordArray(principal.toUint8Array()));
    const subBuffer = Buffer.from(SUB_ACCOUNT_ZERO);
    if (subAccount) {
      subBuffer.writeUInt32BE(subAccount);
    }
    sha.update(byteArrayToWordArray(subBuffer));
    const hash = sha.finalize();
    const byteArray = wordArrayToByteArray(hash, 28);
    const checksum = generateChecksum(byteArray);
    const val = checksum + hash.toString();
    return val;
  } catch (error) {
    console.log(error)
    return false
  }
};

export async function disconnect_force() {
  try {
    await window.ic.bitfinityWallet.disconnect()
  } catch (e) {
    console.warn(e)
  }
  try {
    await window.ic.plug.disconnect()
  } catch (e) {
    console.warn(e)
  }
  return true;
};

export { getTokenIdentifier, getAccountIdentifier }