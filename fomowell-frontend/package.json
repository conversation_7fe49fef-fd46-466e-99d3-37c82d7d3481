{"name": "vite-mui-ts", "version": "1.0.0", "description": "TypeScript + React + Redux + MUI + RRD + ESLint + Prettier => Boilerplate", "license": "MIT", "author": "emrecil <<EMAIL>>", "keywords": ["react", "boilerplate", "typescript", "starter", "vite", "redux", "material-ui", "rrd", "prettier"], "repository": {"type": "git", "url": "https://github.com/emre-cil/vite-mui-ts.git"}, "bugs": {"url": "https://github.com/emre-cil/vite-mui-ts/issues"}, "homepage": "https://github.com/emre-cil/vite-mui-ts#readme", "scripts": {"dev": "cross-env DFX_NETWORK=local vite --mode=development ", "online": "cross-env DFX_NETWORK=ic vite --mode=production ", "start": "vite --open", "host": "vite --open --host", "build": "node build-script.js"}, "dependencies": {"@astrox/sdk-web": "^0.1.41", "@astrox/sdk-webview": "^0.1.41", "@babel/plugin-transform-runtime": "^7.24.3", "@babel/runtime": "^7.24.5", "@dfinity/agent": "^1.3.0", "@dfinity/auth-client": "^2.1.3", "@dfinity/candid": "^1.3.0", "@dfinity/ledger-icp": "^2.2.3", "@dfinity/principal": "^1.3.0", "@dfinity/utils": "^2.2.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.0", "@fort-major/msq-client": "^0.3.22", "@mui/icons-material": "^5.15.13", "@mui/lab": "^5.0.0-alpha.170", "@mui/material": "^5.15.13", "@rainbow-me/rainbowkit": "^2.2.4", "@reduxjs/toolkit": "^2.2.1", "@rollup/plugin-json": "^6.1.0", "@tanstack/react-query": "^5.62.8", "@tanstack/react-query-devtools": "^5.62.8", "@tanstack/react-table": "^8.20.6", "@types/big.js": "^6.2.2", "@types/crypto-js": "^4.2.2", "@types/lodash": "^4.17.4", "@types/md5": "^2.3.5", "@types/node": "^22.13.1", "artemis-web3-adapter": "file:artemis-web3-adapter", "axios": "^1.7.2", "big.js": "^6.2.1", "browserify-fs": "^1.0.0", "buffer": "^6.0.3", "buffer-crc32": "^1.0.0", "classnames": "^2.5.1", "clsx": "^2.1.1", "cross-env": "^7.0.3", "crypto-js": "^4.2.0", "dayjs": "^1.11.11", "ethers": "^6.13.5", "ic-stoic-identity": "^6.0.0", "less": "^4.2.0", "lightweight-charts": "^4.1.6", "lodash": "^4.17.21", "md5": "^2.3.0", "mobx": "^6.12.3", "mobx-react": "^9.1.1", "mobx-react-lite": "^4.0.7", "npm": "^10.8.0", "path-browserify": "^1.0.1", "react": "^18.2.0", "react-copy-to-clipboard": "^5.1.0", "react-dom": "^18.2.0", "react-redux": "^9.1.0", "react-router-dom": "^6.22.3", "tailwind-merge": "^3.0.2", "url": "^0.11.3", "usehooks-ts": "^3.1.1", "wagmi": "^2.14.15", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.24.0", "@types/react": "^18.2.67", "@types/react-dom": "^18.2.22", "@typescript-eslint/eslint-plugin": "^7.3.0", "@typescript-eslint/parser": "^7.3.0", "@vitejs/plugin-react-swc": "^3.6.0", "autoprefixer": "^10.4.20", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-react": "^7.34.1", "patch-package": "^8.0.0", "postcss": "^8.4.49", "postinstall-postinstall": "^2.1.0", "prettier": "^3.2.5", "tailwindcss": "^3.4.17", "typescript": "^5.4.2", "vite": "^5.1.6", "vitest": "^1.4.0"}, "packageManager": "pnpm@8.6.2+sha512.0e68307be4f7e17afa5186932d74a984f4bba24e21d843e46874041fa8fb512a00936d42cee780743d6740b9162700e766426e721a342db2a7b49fbd079c6551"}