import { useQuery } from '@tanstack/react-query';
import { getHoldersList, getTokenCandlesticks, getTokenItemInfo, getTrades } from '@/api/base_api';
import { TokenItem } from '@/api/types';

export const useGetTokenInfo = (token_id: string) => {
    return useQuery({
        queryKey: ['tokenInfo', token_id],
        queryFn: () => getTokenItemInfo(token_id),
        enabled: !!token_id,
    });
};

// export const useTokenComments = (token_id: string, limit: number, page: number) => {
//   return useQuery({
//       queryKey: ['tokenComments', CORE_CANISTER_ID, token_id, limit, page],
//       queryFn: () => getTokenComments(anonymous, { token_id: token_id, page, limit }),
//   });
// };

export const useTokenHolders = (token_id: string) => {
    return useQuery({
        queryKey: ['tokenHolders', token_id],
        queryFn: () => getHoldersList(token_id),
        refetchInterval: 10000,
    });
};

export const useTokenCandle = (token_id: string) => {
    return useQuery({
        queryKey: ['tokenCandle', token_id],
        queryFn: () => getTokenCandlesticks(token_id),
        refetchInterval: 10000,
    });
};

export const useTokenTransactions = (token_id: string) => {
    return useQuery({
        queryKey: ['tokenTransactions', token_id],
        queryFn: () => getTrades(token_id),
        refetchInterval: 10000,
    });
};
