import { getOrderHistory, orderRefound } from "@/api/fomowell_launcher";
import type { Principal } from "@dfinity/principal";
import { useMutation, useQuery } from "@tanstack/react-query";

export const useGetOrderHistory = (pid?: string) => {
	return useQuery({
		queryKey: ["orderHistory", pid],
		queryFn: () => {
			if (!pid) {
				throw new Error("pid is required");
			}
			return getOrderHistory(pid);
		},
		enabled: !!pid,
	});
};
export const useOrderRefound = () => {
	return useMutation({
		mutationFn: ({
			orderIdx,
			userPid,
			memo,
		}: { orderIdx: bigint; userPid: Principal; memo: string }) =>
			orderRefound(orderIdx, userPid, memo),
	});
};
