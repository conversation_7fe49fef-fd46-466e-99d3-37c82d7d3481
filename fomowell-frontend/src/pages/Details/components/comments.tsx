import styles from '../index.module.less';
import { Box, CircularProgress, Fade, IconButton, Input, InputBase, InputLabel, Modal } from '@mui/material';
import { TokenAllInfo } from '@/api/types';
import { LoadingButton } from '@mui/lab';
import { useEffect, useState } from 'react';
import { getImgUrl } from '@/utils/getImgUrl';
import ImagePreview from '@/components/PicturePreview';
import WarningAmberIcon from '@mui/icons-material/WarningAmber';
import { truncateString } from '@/utils/principal';
import { Comments, CommentsCreate } from '@/canisters/fomowell_project/fomowell_project.did';
import Message from '@/components/Snackbar/message';
import chooseFile from '@/assets/home/<USER>';
import { QeqImg } from '@/api/img_request';
import wellToken from '@/assets/welltoken.png';
import { add_comment, get_comments_by_index } from '@/api/fomowell_project';

const ModolStyle = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    backgroundColor: '#262939',
    borderRadius: '8px',
    borderColor: '#262939',
    // width: '400px',
    pt: 2,
    px: 4,
    pb: 3,
    // '.MuiBackdrop-root': {
    //   touchAction: 'none',
    // },
};
const TokenComments = ({ tokenInfo }: { tokenInfo: TokenAllInfo | undefined }) => {
    if (!tokenInfo) return null;

    const [addComment, setAddComment] = useState(false);
    const [LeftFomoContentList, setLeftFomoContentList] = useState<Array<Comments>>([]);
    const [currentPage, setCurrentPage] = useState<number>(1);
    const [loading, setLoading] = useState<boolean>(false);

    const [addCommentParams, setAddCommentParams] = useState<CommentsCreate>({
        // user_pid: Principal.fromText('drv6s-haaaa-aaaag-albcq-cai'),
        content: '',
        image_url: [''],
        extended: [],
    });
    const [EditImg, setEditImg] = useState<File>();
    const [fileName, setFileName] = useState('No file chosen');
    const [isPostReply, setIsPostReply] = useState(false);

    const [initialized, setInitialized] = useState(false);

    const BtnCloseAddModal = () => {
        setAddComment(false);
    };
    const BtnRightReply = () => {
        setAddComment(true);
    };

    const CheckInputValue = () => {
        for (const key in addCommentParams) {
            if (addCommentParams[key as keyof CommentsCreate] == '' && key != 'extended') {
                // props.onRouteChange({ type: 'error', content: `Please enter your ${key}` });
                Message.error(`Please enter your ${key}`);
                return false;
            }
        }
        return true;
    };

    useEffect(() => {
        if (initialized && addCommentParams?.image_url[0]) {
            if (CheckInputValue()) {
                ReqAddComment('img');
            } else {
                setIsPostReply(false);
            }
        }
    }, [addCommentParams?.image_url[0], initialized]);

    const BtnPhoto = (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files) {
            Message.error('file error');
            return;
        }
        if (e.target.files[0].size > 5 * 1024 * 1024) {
            Message.error('Image must smaller than 5MB! and You can only upload JPG/PNG/GIF file!');
            return;
        } else if (
            !['JPG', 'PNG', 'GIF', 'image/jpeg', 'image/png', 'image/gif', 'image/jpg'].includes(e.target.files[0].type)
        ) {
            Message.error('Image must smaller than 5MB! and You can only upload JPG/PNG/GIF file!');
            return;
        }
        const reader: any = new FileReader();
        reader.readAsDataURL(e.target.files[0]);
        reader.onload = function () {
            const newUrl = this.result;
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            img.src = newUrl;
            let data = '';
            img.onload = function () {
                if (!e.target.files) {
                    Message.error('file error');
                    return;
                }
                // let width = img.width;
                canvas.width = 100;
                canvas.height = 100;
                ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
                // Convert to base64 quality to image compression quality. The smaller the value between 0 and 1, the larger the compression, the poorer the image quality
                data = canvas.toDataURL(e.target.files[0].type, 0.3);
                console.debug('🚀 ~ BtnPhoto ~ data:', data);
                setFileName(e.target?.files[0].name);
                // setEditImg(data);
            };
        };
        setEditImg(e.target.files[0]);
        return () => {};
    };

    const BtnPostReply = async () => {
        if (!addCommentParams.content) {
            // Message.error(`Please enter your comment`);
            return;
        }
        if (EditImg) {
            try {
                const res = await QeqImg(EditImg!);
                setAddCommentParams({
                    ...addCommentParams,
                    image_url: [res.reference],
                });
                setInitialized(true);
            } catch (error) {
                setEditImg(undefined);
                setFileName('');
                setIsPostReply(false);
                Message.error('Upload failure');
            }
        } else {
            setAddCommentParams({ ...addCommentParams, image_url: [] });
            ReqAddComment('');
        }
    };

    const ReqAddComment = async (img: string) => {
        try {
            const res = await add_comment(
                tokenInfo._id.toString(),
                img ? addCommentParams : { ...addCommentParams, image_url: [] },
            );
            if ('Ok' in res) {
                // props.onRouteChange({ type: 'success', content: 'success' });
                Message.success('Success!');
                setEditImg(undefined);
                setFileName('');
                getCommentsByIndex(0);
                // const name = await getBaseUserInfo(appStore.userId);

                // setusertoken(name[0] ? Number(name[0].user_points) : '');
                // setlockToken(name[0] ? Number(name[0].user_pre_reward_points) : null);
            } else {
                // props.onRouteChange({ type: 'success', content: 'error' });
                Message.error('error');
            }
            setIsPostReply(false);
            setTimeout(() => {
                setAddComment(false);
            }, 800);
        } catch (error: any) {
            console.log(error);
            if (error && error.message) {
                const match = error.message.match(/Reject text: (.*)/);
                if (match && match[1]) {
                    Message.error(match[1]);
                } else {
                    Message.error('error');
                }
            }
            setTimeout(() => {
                setAddComment(false);
                setIsPostReply(false);
            }, 800);
        }

        // if (appStore.userId) {
        //     setIsPostReply(true);
        // } else {
        //     // props.onWellModal(true);
        // }
    };

    const getCommentsByIndex = async (start: number) => {
        setLoading(true);
        if (!tokenInfo) {
            setLoading(false);
            return;
        }
        const res = await get_comments_by_index(tokenInfo._id.toString(), {
            limit: BigInt(15),
            start: BigInt(start),
        });
        if (res.fomo_vec.length === 0) {
            setLoading(false);
            // setisnull(false);
            // console.log(res.fomo_vec);

            return;
        }
        setLeftFomoContentList((prevItems) => {
            const existingIds = new Set(
                prevItems.map((item) => {
                    return item.comment_idx.toString();
                }),
            );
            // Filter the newly retrieved data for elements that did not exist in the original list
            const uniqueItems = res.fomo_vec.filter((item) => !existingIds.has(item.comment_idx.toString()));
            // Adds non-duplicate elements to the original list
            // setUserInfoList
            return [...prevItems, ...uniqueItems];
        });

        // setLeftFomoContentList(res.fomo_vec);
        setLoading(false);
    };

    return (
        <>
            <div className={styles.icpInfoBottom}>
                <div className={styles.bottomCard}>
                    <div className={styles.RightReply} onClick={BtnRightReply}>
                        Post a reply
                    </div>
                    <div className={styles.top}>
                        <div className={styles.UserInfo}>
                            <img className={styles.UserIcon} src={getImgUrl('')} />
                            <div
                                className={styles.name}
                                // onClick={() => BtnGoUserInfo(LeftUsername?.user_pid.toString()!)}
                            >
                                {truncateString(tokenInfo.owner, 10)}
                            </div>
                        </div>
                        {/* <div className={styles.Time}>{handlTimeFn(curFomoInfo[0]?.fomo_project.create_time)}</div> */}
                    </div>
                    <div className={styles.bottom}>
                        <ImagePreview
                            className={styles.icpItemIcon}
                            src={tokenInfo.uri?.startsWith('http') ? tokenInfo.uri : getImgUrl(tokenInfo.uri || '')}
                            alt=""
                        />
                        <div className={styles.bottomAll}>
                            <div className={styles.top}>
                                {tokenInfo.name} (ticker:
                                {tokenInfo.symbol}){' '}
                                {/* <div
                                    className={styles.cyleslow}
                                    style={{ display: showaddcylesimg.fomo ? '' : 'none' }}
                                >
                                    <WarningAmberIcon style={{ color: 'red', marginLeft: '10px' }}></WarningAmberIcon>
                                    <span style={{ color: 'red', marginLeft: '5px' }}>
                                        Running low on cycles, this token risks delisting!
                                    </span>
                                </div> */}
                            </div>
                            <div className={styles.bottom}>{tokenInfo.description}</div>
                        </div>
                    </div>
                </div>

                {/* {LeftFomoContentList?.map((item) => {
                    return (
                        <div className={styles.contentList} key={item.create_time}>
                            <div className={styles.top}>
                                <div className={styles.UserNameTime}>
                                    <img
                                        src={
                                            userInfoList[item.user_pid.toString()]?.avatar
                                                ? getImgUrl(userInfoList[item.user_pid.toString()]?.avatar)
                                                : ''
                                        }
                                        alt=""
                                    />
                                    <div
                                        className={styles.name}
                                        onClick={() => BtnGoUserInfo(item.user_pid.toString())}
                                    >
                                        {userInfoList[item.user_pid.toString()]?.user_name}
                                    </div>
                                    <div className={styles.time}>{handlTimeFn(item.create_time)}</div>
                                </div>
                                <div className={styles.contentOrImg}>
                                    <ImagePreview src={item.image_url[0] ? getImgUrl(item.image_url[0]!) : ''} alt="" />

                                    <div className={styles.buttom}>{item.content}</div>
                                </div>
                            </div>
                        </div>
                    );
                })}
                <CircularProgress
                    thickness={4}
                    size={20}
                    sx={{
                        color: '#fff',
                        borderRadius: '50%',
                        display: loading ? '' : 'none',
                        marginLeft: '45%',
                        translate: '0px 10px',
                    }}
                /> */}
            </div>

            <Modal
                className={styles.addCommentMoadl}
                open={addComment}
                onClose={BtnCloseAddModal}
                style={{ borderColor: '#262939' }}
            >
                <Fade
                    in={addComment}
                    style={{
                        position: 'relative',
                        width: '300px',
                        outline: 'none',
                    }}
                >
                    <Box
                        sx={{
                            ...ModolStyle,
                        }}
                    >
                        <div className={styles.addComment}>
                            <InputLabel required className={styles.LabelCom}>
                                add a comment
                            </InputLabel>
                            <InputBase
                                rows="5"
                                multiline
                                className={styles.inputCoin}
                                sx={{
                                    '.css-3b6ca1-MuiInputBase-input': {
                                        border: '1px soild red',
                                    },
                                }}
                                onChange={(e) =>
                                    setAddCommentParams({
                                        ...addCommentParams,
                                        content: e.target.value,
                                    })
                                }
                                // required
                            ></InputBase>
                        </div>
                        <div className={styles.addImg}>
                            <InputLabel className={styles.LabelCom}>image</InputLabel>
                            <InputBase
                                value={fileName}
                                required
                                style={{ color: '#9EBADF' }}
                                // onChange={(e) => setAddCommentParams({ ...addCommentParams, image_url: [e.target.value] })}
                                sx={{
                                    '.MuiInputBase-inputAdornedStart': {
                                        aspectRatio: '0',
                                        fontSize: '14px',
                                    },
                                }}
                                startAdornment={
                                    <label htmlFor="icon-button-file-icpInfo" className={styles.UploadBtn}>
                                        <Input
                                            // accept="image/*"
                                            id="icon-button-file-icpInfo"
                                            type="file"
                                            style={{ display: 'none', height: '48px' }}
                                            inputProps={{
                                                style: { color: '#fff' },
                                            }}
                                            onChange={BtnPhoto}
                                        />
                                        <IconButton
                                            color="primary"
                                            aria-label="upload picture"
                                            component="div"
                                            style={{ aspectRatio: '0' }}
                                        >
                                            <img src={chooseFile} style={{ cursor: 'pointer', width: '120px' }}></img>
                                        </IconButton>
                                    </label>
                                    // <Input position="start" accept="image/*" onChange={BtnPhoto} type="file">
                                    //   <img src={chooseFile} style={{ cursor: 'pointer' }}></img>
                                    // </Input>
                                }
                                className={styles.inputCoin}
                            ></InputBase>
                            <LoadingButton
                                id="postreply"
                                loading={isPostReply}
                                className={styles.postreply}
                                onClick={BtnPostReply}
                                style={{
                                    pointerEvents: addCommentParams.content ? 'unset' : 'none',
                                    backgroundImage: !addCommentParams.content
                                        ? 'linear-gradient(235deg, #4c516c 0%, #4e5082 100%)'
                                        : 'linear-gradient(270deg, #A25FFF 0%, #6931FF 100%)',
                                }}
                            >
                                <span style={{ fontSize: '14px' }}>
                                    {addCommentParams.content ? 'post reply' : 'Enter a comment'}
                                </span>
                                <span style={{ display: addCommentParams.content ? '' : 'none' }}>
                                    Requires: 6
                                    <img src={wellToken} style={{ display: isPostReply ? 'none' : '' }}></img>
                                </span>
                            </LoadingButton>

                            <div className={styles.cancel} onClick={BtnCloseAddModal}>
                                cancel
                            </div>
                        </div>
                    </Box>
                </Fade>
            </Modal>
        </>
    );
};

export default TokenComments;
