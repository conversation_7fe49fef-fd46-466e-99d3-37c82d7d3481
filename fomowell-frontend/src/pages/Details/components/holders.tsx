import { MAX_TOKEN_TO_SHOOT } from '@/utils/env';
import { BASE_CHAIN_EXPLORE, routerAddress } from '@/api/initWallet';
// import { cn } from '@/utils/utils';
import { HolderItem, TokenAllInfo } from '@/api/types';
import styles from '../index.module.less';
import { Tooltip } from '@mui/material';
import { truncateString } from '@/utils/principal';

const Holders = ({
    tokenInfo,
    // limit,
    // setPage,
    data,
}: {
    tokenInfo: TokenAllInfo | undefined;
    limit?: number;
    setPage?: (page: number) => void;
    data: HolderItem[] | undefined;
}) => {
    // const handlePageClick = (event: { selected: number }) => {
    //     setPage(event.selected + 1);
    // };

    // 处理并排序数据
    const currentList = data
        ?.map((item) => ({
            ...item,
            isPool: item.from === routerAddress,
            isDev: item.from === tokenInfo?.owner,
            // 计算百分比用于排序和显示
            percentage: (Number(item.balance) / MAX_TOKEN_TO_SHOOT) * 100,
        }))
        // 按照 balance 降序排列
        .sort((a, b) => b.percentage - a.percentage);

    // const total = data?.length;
    // const pageCount = total ? Math.ceil(total / limit) : 0;

    return (
        <div className={styles.kingHillProgress}>
            <div className={styles.details}>
                <div className={styles.Header}>Holder distribution</div>
                <div className={styles.AlldetailItem}>
                    {currentList?.map((item, index) => {
                        return (
                            <div className={styles.detailItem} key={index}>
                                <div className={styles.left}>
                                    <div className={styles.sortNum}>{index + 1}.</div>
                                    <Tooltip title={item.from.toString()} placement="top">
                                        <div className={styles.UserName}>{truncateString(item.from, 10)}</div>
                                    </Tooltip>
                                    <div className={styles.ItemName}>
                                        {item.isPool ? `🏦 (pool)` : item.isDev ? `🤵‍(dev)` : ''}
                                    </div>
                                </div>
                                <div className={styles.Num}>{item.percentage.toFixed(3)}%</div>
                            </div>
                        );
                    })}
                </div>
            </div>
        </div>
    );
};

export default Holders;
