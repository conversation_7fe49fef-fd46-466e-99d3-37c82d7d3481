import { totalSupplyCount } from '@/api/initWallet';
// import { useAccount, useBalance } from 'wagmi';
import styles from '../index.module.less';
import { Box } from '@mui/material';
import LinearProgress, { LinearProgressProps } from '@mui/material/LinearProgress';
import { TokenAllInfo } from '@/api/types';
import X from '@/assets/home/<USER>';
import buy from '@/assets/icpInfo/BuyIcon.png';
import linkIcon from '@/assets/icpInfo/linkIcon.png';

function LinearProgressWithLabel(props: LinearProgressProps & { value: number }) {
    return (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <Box sx={{ width: '100%', mr: 1, marginTop: '10px' }}>
                <LinearProgress
                    variant="determinate"
                    {...props}
                    sx={{
                        '.css-buamxv-MuiLinearProgress-bar1': {
                            backgroundColor: '#00CF26',
                            borderRadius: '8px',
                        },
                    }}
                    style={{
                        height: '10px',
                        background: '#47494b',
                        borderRadius: '8px',
                    }}
                />
            </Box>
        </Box>
    );
}

const BondingCurve = ({ tokenInfo }: { tokenInfo: TokenAllInfo | undefined }) => {
    // const { address } = useAccount();
    // const { data: balance } = useBalance({
    //     address,
    //     token: tokenInfo?.address,
    // });

    const btnToWeb = (url: string) => {
        url && window.open(url, '_blank');
    };

    const percentage = tokenInfo?.market_cap_eth ? Number(tokenInfo?.market_cap_eth) / totalSupplyCount : 0;

    // const formatted24HVolume = tokenInfo?.volume_24h
    //     ? Number(formatUnits(tokenInfo?.volume_24h, getLedger{symbol}Canister().decimals))
    //     : 0;
    // const mc = tokenInfo?.price ? Number(tokenInfo?.price) * 1_000_000_000 : 0;
    return (
        <>
            <div className="my-3">
                <div
                    className={styles.icpConnection}
                    style={{
                        display: tokenInfo?.twitterLink ? '' : 'none',
                        cursor: 'pointer',
                    }}
                    onClick={() => btnToWeb(tokenInfo?.twitterLink || '')}
                >
                    <img className={styles.ConnectionIcon} src={X} />
                    <div className={styles.ConnectionName}>{tokenInfo?.twitterLink}</div>
                </div>
                <div
                    className={styles.icpConnection}
                    style={{
                        display: tokenInfo?.telegramLink ? '' : 'none',
                        cursor: 'pointer',
                    }}
                    onClick={() => btnToWeb(tokenInfo?.telegramLink || '')}
                >
                    <img className={styles.ConnectionIcon} src={buy} />
                    <div className={styles.ConnectionName}>{tokenInfo?.telegramLink}</div>
                </div>
                <div
                    className={styles.icpConnection}
                    style={{
                        display: tokenInfo?.website ? '' : 'none',
                        cursor: 'pointer',
                    }}
                    onClick={() => btnToWeb(tokenInfo?.website || '')}
                >
                    <img className={styles.ConnectionIcon} src={linkIcon} />
                    <div className={styles.ConnectionName}>{tokenInfo?.website}</div>
                </div>
            </div>
            <div className={styles.poolProgress}>
                <div className={styles.PlooNum}>
                    well digging progress:{' '}
                    <div className={styles.Num}>
                        ({percentage && percentage > 1 ? 100 : (percentage * 100).toFixed(2)}
                        %)
                    </div>
                </div>
                <LinearProgressWithLabel value={percentage && percentage > 1 ? 100 : percentage * 100} />
                <div className={styles.Warn}>
                    when the bonding curve reaches <span className="text-white">100%</span>, a token ledger is created.
                    all the liquidity is deposited in an <span className="text-white">uniswap</span> pool.
                    <br />
                    <br />
                    Well digging progress will accelerate as the token price increases.
                    <br />
                    <br />
                    there are tokens still available for sale in the bonding curve and there is in the bonding curve.
                </div>
            </div>
        </>
    );
};

export default BondingCurve;
