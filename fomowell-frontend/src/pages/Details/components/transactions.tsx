import dayjs from 'dayjs';
// import _ from 'lodash';
import { BASE_CHAIN_EXPLORE, routerAddress } from '@/api/initWallet';
import { cn } from '@/utils/utils';
import { TokenAllInfo, TransactionItem } from '@/api/types';
import { truncateString } from '@/utils/principal';
import { handleShowPrice } from '@/utils/common';
import relativeTime from 'dayjs/plugin/relativeTime';
import { Pagination } from '@mui/material';

dayjs.extend(relativeTime); // 加载插件

const Transactions = ({
    tokenInfo,
    limit,
    page,
    setPage,
    data,
    // refetch,
}: {
    tokenInfo: TokenAllInfo | undefined;
    limit: number;
    page: number;
    setPage: (page: number) => void;
    data: TransactionItem[] | undefined;
    // refetch: () => void;
}) => {
    if (!tokenInfo) return null;
    // const newData = _.orderBy(data, ['tokenAmount'], ['desc']);

    const handlePageClick = (page: number) => {
        console.log('🚀 ~ handlePageClick ~ page:', page);
        setPage(page);
    };

    const currentList = data?.map((item) => ({
        ...item,
        isPool: item.from === routerAddress,
        isDev: item.from === tokenInfo?.owner,
    }));
    const total = data?.length;
    const pageCount = total ? Math.ceil(total / limit) : 0;

    // 计算当前页应该显示的数据
    const paginatedList = currentList ? currentList.slice((page - 1) * limit, page * limit) : [];

    return (
        <div className="mt-10">
            <div className="flex gap-2 justify-start items-center font-bold text-white text-md">
                <div>Transactions</div>
                {Number(total) > 0 && <div className="text-[#FBD536]">({total})</div>}
            </div>
            <div className="mt-3 w-full rounded-lg bg-[#232634]">
                <div className="flex items-center justify-between border-b border-[#2B2B2B] px-4 py-4 text-sm text-[#A2A2A2]">
                    <div className="w-[120px] text-left">Buy/Sell</div>
                    <div>Holder</div>
                    <div className="w-[120px] text-right">Amount</div>
                    <div className="w-[120px] text-right">Date</div>
                </div>

                <div className="w-full">
                    {paginatedList?.map((item, index) => {
                        return (
                            <div
                                key={`holders-${item.from}_${index}`}
                                className="flex items-center justify-between p-4 text-sm text-[#A2A2A2]"
                            >
                                <div className="w-[100px] text-left">
                                    {item.event === 'TokenBought' ? 'Buy' : 'Sell'}
                                </div>
                                <div className="flex gap-2 justify-start items-center text-left">
                                    <a
                                        href={`${BASE_CHAIN_EXPLORE}/address/${item.from}`}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="cursor-pointer text-[#FBD536] underline underline-offset-4 hover:opacity-80"
                                    >
                                        {truncateString(item.from, 10)}
                                    </a>
                                </div>

                                <div
                                    className={cn(
                                        'w-[120px] text-right',
                                        item.event === 'TokenBought' ? 'text-[#0BB481]' : 'text-[#DC5305]',
                                    )}
                                >
                                    {item.event === 'TokenBought' ? '+' : '-'}
                                    {handleShowPrice(item.tokenAmount, 2)}
                                </div>
                                <div className={cn('text-right w-[120px]')}>
                                    {item.timestamp ? dayjs(Number(item.timestamp) * 1000).fromNow(true) : '-'}
                                </div>
                            </div>
                        );
                    })}
                </div>

                <div className="flex justify-center items-center pb-5">
                    <Pagination
                        count={pageCount}
                        color="primary"
                        size="small"
                        sx={{
                            '.MuiPaginationItem-colorPrimary': {
                                color: '#fff',
                            },
                        }}
                        onChange={(e, val) => handlePageClick(val)}
                    />
                </div>
            </div>
        </div>
    );
};

export default Transactions;
