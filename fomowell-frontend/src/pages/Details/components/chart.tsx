import { BigNumber } from 'bignumber.js';
import { createChart } from 'lightweight-charts';
import type { IChartApi } from 'lightweight-charts';
import _ from 'lodash';
import { useCallback, useEffect, useRef, useState } from 'react';
import { BASE_CHAIN_EXPLORE, getSymbol } from '@/api/initWallet';
import { TokenItem } from '@/api/types';
import styles from '../index.module.less';
import { handleShowPrice } from '@/utils/common';
import { truncateString } from '@/utils/principal';

type Interval = '1m' | '5m' | '15m' | '30m' | '1h' | '4h' | '1d';

// function genData(timestamp = new Date().getTime(), length = 800) {
//   let basePrice = 2 / 1e10;
//   timestamp = Math.floor(timestamp / 1000 / 60) * 60 * 1000 - length * 60 * 1000;
//   const dataList = [];
//   for (let i = 0; i < length; i++) {
//     const prices = [];
//     for (let j = 0; j < 4; j++) {
//       prices.push(basePrice + Math.random() * 4 - 2);
//     }
//     prices.sort();
//     const open = +prices[Math.round(Math.random() * 3)].toFixed(10);
//     const high = +prices[3].toFixed(10);
//     const low = +prices[0].toFixed(10);
//     const close = +prices[Math.round(Math.random() * 3)].toFixed(10);
//     // const volume = Math.round(Math.random() * 100) + 10
//     // const turnover = (open + high + low + close) / 4 * volume
//     dataList.push({
//       timestamp,
//       open,
//       high,
//       low,
//       close,
//       // , volume, turnover
//     });

//     basePrice = close;
//     timestamp += 60 * 1000;
//   }
//   return dataList;
// }

function DetailsChart({
    tokenInfo,
    data,
    refetch,
}: {
    tokenInfo: TokenItem | undefined;
    data: any[] | undefined;
    refetch: () => void;
}) {
    if (!tokenInfo) return null;

    // data = genData();
    const [interval, setInterval] = useState<Interval>('5m');
    const containerRef = useRef<HTMLDivElement>(null);
    const chartRef = useRef<IChartApi>();
    const symbol = getSymbol();

    const intervals: Array<{ value: Interval; label: string; seconds: number }> = [
        { value: '1m', label: '1m', seconds: 60 },
        { value: '5m', label: '5m', seconds: 300 },
        { value: '15m', label: '15m', seconds: 900 },
        { value: '30m', label: '30m', seconds: 1800 },
        { value: '1h', label: '1H', seconds: 3600 },
        { value: '4h', label: '4H', seconds: 14400 },
        { value: '1d', label: '1D', seconds: 86400 },
        // { value: '1w', label: '1W', seconds: 604800 },
    ];

    const handleIntervalChange = useCallback(
        async (newInterval: Interval) => {
            setInterval(newInterval);
            refetch();
        },
        [refetch],
    );

    // biome-ignore lint/correctness/useExhaustiveDependencies: <explanation>
    useEffect(() => {
        if (!containerRef.current || !data) return;

        // Create chart instance
        const chart = createChart(containerRef.current, {
            layout: {
                background: { color: '#232634' },
                textColor: '#d1d4dc',
            },
            grid: {
                vertLines: { color: 'rgba(42, 46, 57, 0.5)' },
                horzLines: { color: 'rgba(42, 46, 57, 0.5)' },
            },
            crosshair: {
                mode: 1,
                vertLine: {
                    color: '#758696',
                    width: 1,
                    style: 3,
                    labelBackgroundColor: '#758696',
                },
                horzLine: {
                    color: '#758696',
                    width: 1,
                    style: 3,
                    labelBackgroundColor: '#758696',
                },
            },
            localization: {
                locale: 'en-US',
                dateFormat: 'yyyy/MM/dd',
            },
        });
        chartRef.current = chart;

        // Add candlestick series
        const candlestickSeries = chart.addCandlestickSeries({
            // upColor: '#26a69a',
            // downColor: '#ef5350',
            // borderUpColor: '#26a69a',
            // borderDownColor: '#ef5350',
            // wickUpColor: '#26a69a',
            // wickDownColor: '#ef5350',
            priceFormat: {
                type: 'custom',
                formatter: (price: number) => {
                    if (price <= 0) return '0';

                    const str = price.toString();

                    // 处理科学计数法
                    if (str.includes('e-')) {
                        const [base, exponent] = str.split('e-');
                        const zeros = Number(exponent) - 1;
                        // 移除小数点并获取前两个有效数字
                        const significantDigits = base.replace('.', '').slice(0, 2);
                        return `0.{${zeros}}${significantDigits}`;
                    }

                    // 处理普通小数
                    const match = str.match(/(0\.0*)([1-9].*)/);
                    if (match) {
                        const leadingZeros = match[1];
                        const significantDigits = match[2];
                        const zeroCount = leadingZeros.length - 2; // 减去 "0." 的长度
                        if (zeroCount > 2) {
                            return `0.{${zeroCount}}${significantDigits.slice(0, 2)}`;
                        }
                        return `0.${match[1].slice(2)}${significantDigits.slice(0, 2)}`;
                    }

                    // 处理大于1的数字
                    if (Number(str) >= 1) {
                        return Number(str).toFixed(4);
                    }

                    return str;
                },
                minMove: 1e-8,
            },
        });

        // Process and set data
        const currentInterval = intervals.find((i) => i.value === interval);
        const intervalSeconds = currentInterval?.seconds || 3600;

        // Process and combine data in a single pass
        const processedDataMap = new Map();

        // 首先按时间戳排序，确保数据按时间顺序处理
        const sortedData = _.orderBy(data, [(item) => Number(new Date(item.timestamp).valueOf())], ['asc']);

        for (const candle of sortedData) {
            const timestamp = new Date(candle.timestamp).valueOf() / 1000;
            const intervalTime = Math.floor(timestamp / intervalSeconds) * intervalSeconds;

            const candleData = {
                open: new BigNumber(candle.open),
                high: new BigNumber(candle.high),
                low: new BigNumber(candle.low),
                close: new BigNumber(candle.close),
            };

            if (!processedDataMap.has(intervalTime)) {
                processedDataMap.set(intervalTime, {
                    time: intervalTime,
                    open: candleData.open, // 第一个开盘价
                    high: candleData.high,
                    low: candleData.low,
                    close: candleData.close,
                });
            } else {
                const existing = processedDataMap.get(intervalTime);
                existing.high = BigNumber.max(existing.high, candleData.high);
                existing.low = BigNumber.min(existing.low, candleData.low);
                existing.close = candleData.close; // 最后一个收盘价
            }
        }

        const combinedData = Array.from(processedDataMap.values()).map((candle, index, array) => ({
            time: candle.time,
            open: index > 0 ? array[index - 1].close.toNumber() : candle.open.toNumber(),
            high: candle.high.toNumber(),
            low: candle.low.toNumber(),
            close: candle.close.toNumber(),
        }));

        // 确保最终数据按时间排序
        candlestickSeries.setData(_.orderBy(combinedData, ['time'], ['asc']));

        // Configure price scale
        chart.priceScale('right').applyOptions({
            autoScale: true,
            scaleMargins: {
                top: 0.1,
                bottom: 0.05,
            },
            borderVisible: false,
            entireTextOnly: true,
            mode: 0,
            invertScale: false,
            alignLabels: true,
            ticksVisible: true,
            borderColor: 'rgba(197, 203, 206, 0.5)',
            textColor: '#d1d4dc',
        });

        // Configure time scale
        chart.timeScale().applyOptions({
            timeVisible: true,
            secondsVisible: false,
            borderVisible: false,
        });

        // Fit content
        chart.timeScale().fitContent();

        // Handle resize
        window.addEventListener('resize', () => {
            if (window.innerWidth >= 1440) {
                chartRef.current?.resize(1440 - 160 - 410, 390);
            } else if (window.innerWidth >= 1024 && window.innerWidth < 1440) {
                chartRef.current?.resize(window.innerWidth - 160 - 410, 390);
            } else if (window.innerWidth < 768 || window.innerWidth < 1024) {
                chartRef.current?.resize(window.innerWidth - 40, 390);
            } else {
                chartRef.current?.resize(window.innerWidth - 160 - 410, 390);
            }
        });

        // Cleanup
        return () => {
            chart.remove();
        };
    }, [data, interval, intervals]);

    return (
        <>
            <div className={styles.chartPage}>
                <div className={styles.ChartHeader}>
                    <div className={styles.left}>
                        <span>{tokenInfo.name} </span>
                        <span>Ticker: {tokenInfo.symbol} </span>
                        <span className={styles.Market}>
                            {' '}
                            Market cap: ${handleShowPrice(tokenInfo?.market_cap_eth || 0, 6)}
                        </span>
                    </div>
                    <div className={styles.right}>
                        <span className={styles.created}>created by: </span>

                        <span
                            className={styles.UserName}
                            onClick={() => {
                                window.open(`${BASE_CHAIN_EXPLORE}/address/${tokenInfo?.owner}`, '_blank');
                            }}
                        >
                            {truncateString(tokenInfo?.owner, 10)}
                        </span>
                    </div>
                </div>
                <div className="h-full w-full overflow-hidden rounded-lg bg-[#232634] mt-2">
                    <div className="flex items-center p-4 border-b border-gray-800">
                        <div className="flex gap-1">
                            {intervals.map(({ value, label }) => (
                                <button
                                    type="button"
                                    key={value}
                                    onClick={() => handleIntervalChange(value)}
                                    className={`rounded-md px-3 py-1 text-sm transition-colors ${
                                        interval === value
                                            ? 'bg-[#FBD536] text-black'
                                            : 'text-gray-400 hover:bg-gray-700 hover:text-white'
                                    }`}
                                >
                                    {label}
                                </button>
                            ))}
                        </div>
                    </div>
                    <div ref={containerRef} className="min-h-[274px] w-full md:min-h-[390px]" />
                </div>
            </div>
        </>
    );
}

export default DetailsChart;
