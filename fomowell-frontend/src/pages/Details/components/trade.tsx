import { useInterval } from 'usehooks-ts';
import { useBalance } from 'wagmi';
import { useEffect, useRef, useState } from 'react';
import { useLocation, useParams, useSearchParams } from 'react-router-dom';
import Message from '@/components/Snackbar/message';
import { useWalletInfo } from '@/hooks/useWalletInfo';
import {
    allowanceAndApprove,
    buildBuyTransaction,
    getBuyCalculateEthToToken,
    getSellCalculateTokenToEth,
    getTokenItemInfo,
    getTrades,
    getUserTokenBalance,
    sendTransaction,
} from '@/api/base_api';
import { BASE_CHAIN_ID, getPreSetAmounts, getSymbol, MAX_REFETCH_COUNT, totalSupplyCount } from '@/api/initWallet';
import { BuyPayload, SellPayload, TokenItem, TransactionType } from '@/api/types';
import { cn } from '@/utils/utils';
import styles from '../index.module.less';
import { handleShowPrice } from '@/utils/common';
import { LoadingButton } from '@mui/lab';
import { InputAdornment, InputBase } from '@mui/material';

function Trade({ reload }: { reload: () => void }) {
    const { ercAddress: token, tokenAddress } = useParams<{
        ercAddress: string;
        tokenAddress: string;
    }>();
    const location = useLocation();
    // const [searchParams] = useSearchParams();

    const tokenSymbol = location.state.symbol;
    const symbol = getSymbol();

    // const { tradeSheetOpen } = useModalStore();

    // buy = 1, sell = 2
    const [activeTab, setActiveTab] = useState<1 | 2>(1);
    const [loading, setLoading] = useState<boolean>(false);
    const [coinData, setCoinData] = useState<TokenItem>();
    const [buyAmount, setBuyAmount] = useState<number>(0);
    const [sellAmount, setSellAmount] = useState<number>(0);
    const [swapList, setSwapList] = useState<any>([]);
    const [maxEthCount, setMaxEthCount] = useState<number | string>(0);
    const [balance, setBalance] = useState<number>(0);
    const [count, setCount] = useState<number>(0);
    const [sendSuccess, setSendSuccess] = useState<boolean>(false);
    const [refreshCount, setRefreshCount] = useState<number>(0);
    const preSetAmounts = getPreSetAmounts();
    const { address } = useWalletInfo();
    const { data: userEthBalance, error } = useBalance({
        address, // 使用当前钱包地址
        chainId: BASE_CHAIN_ID,
    });
    const tabRefBuy = useRef<HTMLDivElement>(null);
    const tabRefSell = useRef<HTMLDivElement>(null);

    // todo goto swap
    const show_go_to_swap = false;

    useInterval(() => {
        const newRefreshCount = refreshCount + 1;
        setRefreshCount(newRefreshCount);
    }, 10 * 1000);

    useEffect(() => {
        if (!token) return;
        btnBuy();

        initData();
        getSwapList();
    }, []);

    useEffect(() => {
        if (token) {
            getUserBalance();
        }
    }, [token]);

    useEffect(() => {
        if (loading) return;
        // 定时刷新最大限制
        if (refreshCount > MAX_REFETCH_COUNT) return;

        initData();
        getSwapList();
    }, [refreshCount]);

    const getSwapList = async () => {
        try {
            if (!tokenAddress) return;

            setLoading(true);

            const data = await getTrades(tokenAddress);

            setSwapList(data.holders ? data.holders : []);
            setLoading(false);
        } catch (error) {
            console.debug('🚀 ~ getSwapList ~ error:', error);
            setLoading(false);
        }
    };

    useEffect(() => {
        getCountInfo();
    }, [activeTab, buyAmount, sellAmount]);

    const getCountInfo = async () => {
        try {
            if (!tokenAddress) return;

            if (activeTab === 1 && buyAmount) {
                // 计算可得到的token
                const { tokenAmount } = await getBuyCalculateEthToToken(`${buyAmount}`, tokenAddress);

                if (tokenAmount) {
                    const tokenCount = handleShowPrice(tokenAmount, 3);
                    setCount(tokenCount);
                    return;
                }
            }

            if (activeTab === 2 && sellAmount) {
                // 计算可得到的eth
                const { ethAmount } = await getSellCalculateTokenToEth(`${sellAmount}`, tokenAddress);

                if (ethAmount) {
                    const tokenCount = handleShowPrice(ethAmount, 5);
                    setCount(tokenCount);
                    return;
                }
            }

            setCount(0);
        } catch (error) {
            console.debug('🚀 ~ getCountInfo ~ error:', error);
        }
    };

    const getUserBalance = async () => {
        try {
            if (!tokenAddress || !address) return;

            const data = await getUserTokenBalance(tokenAddress, address);

            setBalance(data && data.balance ? Number(data.balance) : 0);
        } catch (error) {
            console.debug('🚀 ~ getUserBalance ~ error:', error);
        }
    };

    // const onChangeTab = (current: 1 | 2) => {
    //     setActiveTab(current);
    //     setBuyAmount(0);
    //     setSellAmount(0);
    // };

    const initData = async () => {
        try {
            if (!token || !tokenAddress) return;

            setLoading(true);

            const { token: data }: { token: TokenItem } = await getTokenItemInfo(tokenAddress);
            // console.debug('🚀 ~ initData ~ data:', data);

            if (!data) return;

            const { market_cap_eth } = data;
            const maxToBuy = totalSupplyCount - (market_cap_eth || 0);
            if (market_cap_eth && market_cap_eth >= totalSupplyCount) {
                setSendSuccess(true);
            }
            setCoinData(data);
            setMaxEthCount(maxToBuy);
            setLoading(false);
        } catch (error) {
            console.debug('🚀 ~ initData ~ error:', error);
            setLoading(false);
        }
    };

    const reloadALLData = () => {
        initData();
        setBuyAmount(0);
        setSellAmount(0);
        getSwapList();
        getUserBalance();

        reload();
    };

    const setSellCount = (percent: number) => {
        if (Number(balance) === 0) {
            return 0;
        }
        const count = handleShowPrice(Number(balance) * percent, 3);

        setSellAmount(count);
    };

    // 卖出
    const onBuildSellTransaction = async () => {
        try {
            if (!token || !tokenAddress) return;

            const accounts = [address];
            if (!accounts || !accounts[0]) {
                // connect wallet first
                return;
            }

            setLoading(true);
            const payload: SellPayload = {
                from_address: accounts[0],
                address: tokenAddress,
                bc_address: token,
                sell_bc_amount: sellAmount,
            };

            const result = await allowanceAndApprove(payload);

            setLoading(false);
            if (result) {
                Message.success('place trade success');

                reloadALLData();
            } else {
                Message.error('place trade failed');
            }
        } catch (error) {
            console.debug('🚀 ~ onSell ~ error:', error);
            setLoading(false);
        }
    };

    const onBuildBuyTransaction = async () => {
        if (!token || !tokenAddress) return;

        const accounts = [address];
        if (!accounts || !accounts[0]) {
            // connect wallet first
            return;
        }

        setLoading(true);
        const payload: BuyPayload = {
            from_address: accounts[0],
            address: tokenAddress,
            value: buyAmount,
        };

        buildBuyTransaction(payload)
            .then(async (transaction) => {
                await onBuy(transaction);
            })
            .catch((error) => {
                console.debug('🚀 ~ onBuildBuyTransaction ~ error:', error);
                Message.error('place trade failed');
            })
            .finally(() => {
                setLoading(false);
            });
    };

    const onBuy = async (transaction: TransactionType) => {
        try {
            if (!transaction) return;

            setLoading(true);
            const data = await sendTransaction(transaction);
            setLoading(false);
            if (data) {
                Message.success('place trade success');
                reloadALLData();
            } else {
                Message.error('place trade failed');
            }
        } catch (error) {
            console.debug('🚀 ~ onBuy ~ error:', error);
            setLoading(false);
        }
    };

    const btnBuy = () => {
        setActiveTab(1);
        if (tabRefBuy.current && tabRefSell.current) {
            tabRefBuy.current.style.background = 'linear-gradient(270deg, #A25FFF 0%, #6931FF 100%)';
            tabRefSell.current.style.background = '';
            tabRefSell.current.style.backgroundColor = '#34384B';
        }
    };

    const btnSell = () => {
        setActiveTab(2);
        if (tabRefBuy.current && tabRefSell.current) {
            tabRefSell.current.style.background = '#f77170';
            tabRefBuy.current.style.background = '';
            tabRefBuy.current.style.backgroundColor = '#34384B';
        }
    };

    const isNoInputAmount = (activeTab === 1 && !buyAmount) || (activeTab === 2 && !sellAmount);

    const isNotEnough =
        (activeTab === 1 && Number(userEthBalance?.formatted) < buyAmount) ||
        (activeTab === 2 && (sellAmount > balance || !sellAmount));

    const btnDisable = loading || sendSuccess || isNoInputAmount || isNotEnough;

    return (
        <div className={`${styles.BuySell} ${show_go_to_swap ? 'hidden' : ''}`}>
            <div className={styles.BuySellTabs}>
                <div className={styles.tabHeader}>
                    <div ref={tabRefBuy} className={styles.tabName} onClick={btnBuy}>
                        Buy
                    </div>
                    <div ref={tabRefSell} className={styles.tabName} onClick={btnSell}>
                        Sell
                    </div>
                </div>
                <div className={styles.TabContent}>
                    <div
                        className={styles.inputInfo}
                        style={{
                            width: '90%',
                            height: '28px',
                            border: '1px solid rgba(87,61,148,1)',
                            borderRadius: '4px',
                            padding: '10px',
                        }}
                    >
                        <InputBase
                            // value="No file chosen"
                            style={{ color: '#9EBADF', width: '100%' }}
                            placeholder="0.0"
                            type="number"
                            // max={activeTab === 1 ? Number(maxEthCount) : Number(balance)}
                            value={activeTab === 2 ? sellAmount : buyAmount}
                            onChange={(e) => {
                                const value: number = parseFloat(e.target.value);
                                activeTab === 1 && setBuyAmount(value);
                                activeTab === 2 && setSellAmount(value);
                            }}
                            endAdornment={
                                <InputAdornment
                                    position="end"
                                    style={{
                                        fontFamily: '',
                                        fontSize: '14px',
                                        color: '#FFFFFF',
                                        letterSpacing: 0,
                                        fontWeight: 600,
                                    }}
                                >
                                    <div className="text-white">
                                        {activeTab === 1 ? symbol : activeTab === 2 ? `$${tokenSymbol}` : ''}
                                    </div>
                                </InputAdornment>
                            }
                            className={styles.inputCoin}
                        ></InputBase>
                    </div>

                    <div
                        className={styles.TypeTag}
                        style={{
                            display: activeTab === 1 ? 'flex' : 'none',
                        }}
                    >
                        {preSetAmounts.map((item) => (
                            <div
                                key={item.label}
                                onClick={() => setBuyAmount(item.value)}
                                className={styles.TypeTagItem}
                            >
                                {item.label}
                            </div>
                        ))}
                    </div>

                    <div
                        className={styles.TypeTag}
                        style={{
                            display: activeTab === 2 ? 'flex' : 'none',
                        }}
                    >
                        <div onClick={() => setSellCount(0)} className={styles.TypeTagItem}>
                            reset
                        </div>
                        <div onClick={() => setSellCount(0.25)} className={styles.TypeTagItem}>
                            25%
                        </div>
                        <div onClick={() => setSellCount(0.5)} className={styles.TypeTagItem}>
                            50%
                        </div>
                        <div onClick={() => setSellCount(1)} className={styles.TypeTagItem}>
                            100%
                        </div>
                    </div>

                    <div className={styles.icpToSellBase}>
                        {count ? (
                            <span>
                                You will receive ~ {count} {activeTab === 1 ? `$${tokenSymbol}` : symbol}
                            </span>
                        ) : (
                            ''
                        )}
                    </div>

                    <LoadingButton
                        className={styles.BtnPlaceTrade}
                        loading={loading}
                        disabled={btnDisable}
                        onClick={() => {
                            activeTab === 1 && onBuildBuyTransaction();
                            activeTab === 2 && onBuildSellTransaction();
                        }}
                        style={{
                            backgroundImage: btnDisable
                                ? 'linear-gradient(235deg, #4c516c 0%, #4e5082 100%)'
                                : 'linear-gradient(270deg, #A25FFF 0%, #6931FF 100%)',
                            pointerEvents: btnDisable ? 'none' : 'unset',
                            cursor: btnDisable ? 'not-allowed' : 'pointer',
                        }}
                        sx={{
                            color: '#FFFFFF',
                        }}
                    >
                        {isNoInputAmount
                            ? 'Please enter the amount'
                            : isNotEnough
                              ? 'Insufficient balance'
                              : 'Place Trade'}
                    </LoadingButton>

                    <div className={cn('mt-3 text-center', sendSuccess ? 'block' : 'hidden')}>
                        The Pool was migrated to Uniswap V2.
                    </div>
                </div>
            </div>
        </div>
    );
}

export default Trade;
