import { useGetTokenInfo, useTokenCandle, useTokenHolders, useTokenTransactions } from '@/hooks/core';
import { useEffect, useRef, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { observer } from 'mobx-react-lite';
import { messageInfo } from '@/utils/appType';
import styles from './index.module.less';
import Chart from './components/chart';
import Transactions from './components/transactions';
import { TokenAllInfo } from '@/api/types';
import Message from '@/components/Snackbar/message';
import Trade from './components/trade';
import BondingCurve from './components/bondingCurve';
import Holders from './components/holders';
import TokenComments from './components/comments';

interface UserInfoProps {
    //message under components/Snackbar is no longer used
    onRouteChange: (Params: messageInfo) => void;
    onWellModal: (Param: boolean) => void;
    openSelectWell: boolean;
}

const TokenDetails = observer((props: UserInfoProps) => {
    const navigate = useNavigate();
    const { ercAddress, tokenAddress } = useParams<{ ercAddress: string; tokenAddress: string }>();

    if (!ercAddress || !tokenAddress) return null;

    const [searchFomosInit, setSearchFomosInit] = useState(false);

    const { data, refetch } = useGetTokenInfo(tokenAddress);

    const { data: tokenCandle, refetch: refetchCandle } = useTokenCandle(tokenAddress);

    const limit = 10;
    const [page, setPage] = useState(1);
    const { data: tokenHolders, refetch: refetchHolders } = useTokenHolders(tokenAddress);

    const [transactionPage, setTransactionPage] = useState(1);
    const transLimit = 10;
    const { data: tokenTransactions, refetch: refetchTransactions } = useTokenTransactions(tokenAddress);

    const [isLoading, setIsLoading] = useState(true);
    const UserComments = useRef<HTMLDivElement>(null);

    useEffect(() => {
        setIsLoading(true);
        const loadData = async () => {
            if (tokenAddress) {
                await Promise.all([refetch(), refetchCandle(), refetchHolders(), refetchTransactions()]);
                setIsLoading(false);
            }
        };
        loadData();
    }, [tokenAddress, refetch, refetchCandle, refetchHolders]);

    const reload = () => {
        Promise.all([refetch(), refetchCandle(), refetchHolders(), refetchTransactions()]).finally(() => {
            setIsLoading(false);
        });
    };

    const BtnGoBack = () => {
        if (window.history.length > 1) {
            navigate(-1);
        } else {
            navigate('/');
        }
    };

    return (
        <div className={styles.baseInfoMain} ref={UserComments}>
            <div
                className={styles.Back}
                style={{
                    color: '#fff',
                    cursor: 'pointer',
                    paddingTop: '10px',
                    display: data && data.token ? (data.token._id ? '' : 'none') : searchFomosInit ? '' : 'none',
                }}
                onClick={() => BtnGoBack()}
            >
                [ go back ]
            </div>

            <div className={styles.Main}>
                <div
                    className={styles.FomoId}
                    style={{
                        display:
                            data && data.token ? (data && data.token._id ? 'none' : '') : searchFomosInit ? 'none' : '',
                    }}
                >
                    {/* Fomo does not exist */}
                </div>
                <div
                    className={styles.Goback}
                    style={{
                        color: '#fff',
                        cursor: 'pointer',
                        width: '100px',
                        display: data && data.token ? (data.token._id ? 'none' : '') : searchFomosInit ? 'none' : '',
                    }}
                    onClick={() => BtnGoBack()}
                >
                    [ go back ]
                </div>

                <div
                    className={styles.icpInfoLeft}
                    style={{
                        visibility:
                            data && data.token
                                ? data.token._id
                                    ? 'visible'
                                    : 'hidden'
                                : searchFomosInit
                                  ? 'visible'
                                  : 'hidden',
                    }}
                >
                    <Chart tokenInfo={data?.token} data={tokenCandle ? tokenCandle.ohlc : []} refetch={refetchCandle} />
                    <Transactions
                        tokenInfo={data?.token as TokenAllInfo | undefined}
                        // refetch={refetchTransactions}
                        page={transactionPage}
                        setPage={setTransactionPage}
                        limit={transLimit}
                        data={tokenTransactions ? tokenTransactions.holders : []}
                    />
                    {/* <TokenComments tokenInfo={data?.token as TokenAllInfo | undefined} /> */}
                </div>

                <div
                    className={styles.icpInfoRight}
                    style={{
                        visibility:
                            data && data.token
                                ? data.token._id
                                    ? 'visible'
                                    : 'hidden'
                                : searchFomosInit
                                  ? 'visible'
                                  : 'hidden',
                    }}
                >
                    <Trade reload={reload} />

                    <div className={styles.UserProgress}>
                        <BondingCurve tokenInfo={data?.token as TokenAllInfo | undefined} />
                        <Holders
                            tokenInfo={data?.token as TokenAllInfo | undefined}
                            // token_id={tokenAddress}
                            // refetch={refetchHolders}
                            // page={page}
                            setPage={setPage}
                            limit={limit}
                            data={tokenHolders ? tokenHolders.holders : []}
                        />
                    </div>
                </div>
            </div>
        </div>
    );
});

export default TokenDetails;
