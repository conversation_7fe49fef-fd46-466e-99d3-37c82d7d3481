import React, { useEffect } from 'react';
import {
    Paper,
    IconButton,
    InputBase,
    FormControl,
    Select,
    MenuItem,
    SelectChangeEvent,
    InputBaseProps,
} from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import styles from '../index_base.module.less';
import { StyledComponent } from '@emotion/styled';
import { styled } from '@mui/material/styles';

interface SelectsProps {
    handleSortMenuChange: (event: SelectChangeEvent<string>) => void;
    handleOrderMenuChange: (event: SelectChangeEvent<string>) => void;
    setSearchInputVal: (val: string) => void;
    BtnSearch: () => void;
}

type SelectMenu = {
    value: string;
    name: string;
};

const SortMenu: SelectMenu[] = [
    {
        value: 'BumpOrder',
        name: 'sort: bump order',
    },
    {
        value: 'LastReply',
        name: 'sort: last reply',
    },
    {
        value: 'ReplyCount',
        name: 'sort: reply count',
    },
    {
        value: 'MarketCap',
        name: 'sort: market cap',
    },
    {
        value: 'CreationTime',
        name: 'sort: creation time',
    },
];
const OrderMenu: SelectMenu[] = [
    {
        value: 'ASC',
        name: 'order: asc',
    },
    {
        value: 'DESC',
        name: 'order: desc',
    },
];

const BootstrapInput = styled(InputBase)(({ theme }) => ({
    'label + &': {
        marginTop: theme.spacing(3),
    },
    '& .MuiInputBase-input': {
        borderRadius: 8,
        position: 'relative',
        backgroundColor: '#655EA7',
        border: '1px solid #655EA7',
        fontSize: 16,
        padding: '10px 26px 10px 12px',
        '&:focus': {
            borderRadius: 8,
            borderColor: '',
            boxShadow: '',
        },
    },
    '& .MuiSelect-iconOutlined': {
        color: '#fff',
    },
}));

const SelectsAndSearch = ({
    handleSortMenuChange,
    handleOrderMenuChange,
    setSearchInputVal,
    BtnSearch,
}: SelectsProps) => {
    const [SortVal, setSortVal] = React.useState('BumpOrder');
    const [OrderVal, setOrderVal] = React.useState('DESC');
    const SelectsMemoSortChange = (e: SelectChangeEvent<string>) => {
        handleSortMenuChange(e);
        setSortVal(e.target.value);
    };
    const SelectsMemoOrderChange = (e: SelectChangeEvent<string>) => {
        handleOrderMenuChange(e);
        setOrderVal(e.target.value);
    };
    return (
        <>
            <Paper className={styles.SearchBox} sx={{ p: '2px 4px', display: 'flex', alignItems: 'center' }}>
                <InputBase
                    sx={{ ml: 1, flex: 1 }}
                    placeholder="Search by symbol or address"
                    onChange={(e) => setSearchInputVal(e.target.value)}
                    onKeyDown={(e) => {
                        if (e.key === 'Enter') {
                            BtnSearch();
                        }
                    }}
                />
                <IconButton sx={{ p: '10px', color: '#7F7F7F' }} onClick={BtnSearch}>
                    <SearchIcon className={styles.SearchIcon} />
                </IconButton>
            </Paper>

            <div className="mt-4">
                <FormControl sx={{ m: 1 }}>
                    <Select
                        className={styles.FilterBox}
                        value={SortVal}
                        label="SortVal"
                        onChange={SelectsMemoSortChange}
                        sx={{
                            height: '35px',
                            '.MuiInputBase-input': {
                                padding: '8px 10px 8px 10px',
                            },
                        }}
                        input={<BootstrapInput />}
                    >
                        {SortMenu.map((item) => {
                            return (
                                <MenuItem key={item.name} value={item.value}>
                                    {item.name}
                                </MenuItem>
                            );
                        })}
                    </Select>
                </FormControl>
                <FormControl sx={{ m: 1 }}>
                    <Select
                        className={styles.FilterBox}
                        value={OrderVal}
                        label="OrderVal"
                        sx={{
                            height: '35px',
                            '.MuiInputBase-input': {
                                padding: '8px 10px 8px 10px',
                            },
                        }}
                        onChange={SelectsMemoOrderChange}
                        input={<BootstrapInput />}
                    >
                        {OrderMenu.map((item) => {
                            return (
                                <MenuItem key={item.name} value={item.value}>
                                    {item.name}
                                </MenuItem>
                            );
                        })}
                    </Select>
                </FormControl>
            </div>
        </>
    );
};
export default SelectsAndSearch;
