import React, { useEffect, useState } from 'react';
import { Card, CardContent, Skeleton, SelectChangeEvent } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import styles from '../index_base.module.less';
import X from '@/assets/home/<USER>';
import buy from '@/assets/home/<USER>';
import link from '@/assets/home/<USER>';
import champion from '@/assets/home/<USER>';
import { getFomoUserInfo, get_god_of_wells, search_fomos, getBaseUserInfo } from '@/api/fomowell_launcher';
import {
    FomoProject,
    FomoProjectInfo,
    SearchParam,
    UserProfile,
} from '@/canisters/fomowell_launcher/fomowell_launcher.did';
import { Principal } from '@dfinity/principal';
import appStore from '@/store/app';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { get_comments_len } from '@/api/fomowell_project';
import { messageInfo } from '@/utils/appType';
import Message from '@/components/Snackbar/message';
import { getImgUrl } from '@/utils/getImgUrl';
import { formatAmountByUnit, handleShowPrice } from '@/utils/common';
import god_img from '@/assets/home/<USER>';
import Big from 'big.js';
import ICPEx from '@/assets/ICPEx.png';
import sneed from '@/assets/SNEED.png';
import { cycles as fomowell_project_cycles } from '@/api/fomowell_project';
import lowBattering from '@/assets/icpInfo/low gas.png';

import { BASE_CHAIN_EXPLORE } from '@/api/initWallet';
import { TokenItem } from '@/api/types';
import { truncateString } from '@/utils/principal';
import { MAX_COUNT_TO_SHOOT } from '@/utils/env';

export const TokenCardSkeleton = () => {
    return (
        <Card className={styles.BottomUserInfoCard}>
            <CardContent className={styles.CardUserContent}>
                <Skeleton
                    variant="text"
                    sx={{
                        bgcolor: '#655EA7',
                        marginTop: '10px',
                        height: '40px',
                    }}
                />
                <Skeleton variant="circular" width={40} height={40} sx={{ bgcolor: '#655EA7', marginTop: '10px' }} />
                <Skeleton
                    variant="rectangular"
                    width={210}
                    height={118}
                    sx={{ bgcolor: '#655EA7', marginTop: '10px' }}
                />
            </CardContent>
        </Card>
    );
};

export const TopTokenCardSkeleton = () => {
    return (
        <Card
            style={{
                backgroundColor: '#3a3a5a',
                width: '400px',
                height: '220px',
            }}
        >
            <CardContent className={styles.CardUserContent}>
                <Skeleton variant="text" sx={{ bgcolor: '#655EA7', marginTop: '10px', height: '40px' }} />
                <Skeleton variant="circular" width={40} height={40} sx={{ bgcolor: '#655EA7', marginTop: '10px' }} />
                <Skeleton
                    variant="rectangular"
                    width={210}
                    height={80}
                    sx={{ bgcolor: '#655EA7', marginTop: '10px' }}
                />
            </CardContent>
        </Card>
    );
};

export const TokenCard = ({
    FomoItem,
    showTokenInfo,
    // userReplies,
    // fomoInfo,
    // lowBattery,
}: {
    FomoItem: TokenItem;
    showTokenInfo: () => void;
}) => {
    const percentage = FomoItem.market_cap_eth ? Number(FomoItem.market_cap_eth) / MAX_COUNT_TO_SHOOT : 0;

    return (
        <Card className={styles.BottomUserInfoCard} sx={{ padding: 0, margin: 0 }}>
            <CardContent
                className={styles.CardUserContent}
                sx={{
                    '&:last-child': {
                        paddingBottom: 1,
                    },
                    padding: 1,
                }}
            >
                <div className={styles.BtmUserInfoAll} onClick={() => showTokenInfo()}>
                    <div className={styles.BtmUserInfo}>
                        <div className={styles.MainTitle}>
                            {FomoItem.name} [{FomoItem?.symbol}]
                        </div>
                        <div className={styles.createUser}>
                            created by:{' '}
                            <span
                                className={styles.fomoListName}
                                style={{ cursor: 'pointer' }}
                                onClick={() => {
                                    window.open(`${BASE_CHAIN_EXPLORE}/address/${FomoItem?.owner}`, '_blank');
                                }}
                            >
                                {truncateString(FomoItem?.owner, 10)}
                            </span>
                        </div>
                        <div className={styles.Description}>{FomoItem.description}</div>
                        <div className={styles.BtmUserInfoItem}>
                            {FomoItem.twitterLink && (
                                <div className={styles.BtmLeftItem}>
                                    <img className={styles.ItemInfoImg} src={X} />
                                </div>
                            )}
                            {FomoItem.telegramLink && (
                                <div className={styles.BtmLeftItem}>
                                    <img className={styles.ItemInfoImg} src={buy} />
                                </div>
                            )}
                            {FomoItem.website && (
                                <div className={styles.BtmLeftItem}>
                                    <img className={styles.ItemInfoImg} src={link} />
                                </div>
                            )}
                        </div>
                        <div className={styles.amountInfo}>
                            <div>
                                market cap:
                                <span className={styles.amountInfoText}>
                                    {handleShowPrice(FomoItem?.market_cap_eth || 0, 6)} ETH
                                </span>
                                <span
                                    style={{
                                        // todo:
                                        display: percentage >= 1 ? '' : 'none',
                                        // display: FomoItem.god_of_wells_progress >= BigInt(10000) ? '' : 'none',
                                    }}
                                >
                                    [<span>badge:</span>
                                    <img
                                        style={{
                                            width: '18px',
                                            height: '18px',
                                            translate: '1px 3px',
                                        }}
                                        src={champion}
                                    ></img>
                                    {/* <img
                                        style={{
                                            width: '16px',
                                            height: '16px',
                                            marginRight: '1px',
                                            translate: '1px 3px',
                                            display: FomoItem.pool_progress_done_time.length != 0 ? '' : 'none',
                                        }}
                                        src={ICPEx}
                                    />
                                    <img
                                        style={{
                                            width: '16px',
                                            height: '16px',
                                            marginRight: '1px',
                                            translate: '1px 3px',
                                            display:
                                                FomoItem.pool_progress_done_time.length != 0 &&
                                                FomoItem.sneed_dao_lock.length != 0
                                                    ? ''
                                                    : 'none',
                                        }}
                                        src={sneed}
                                    /> */}
                                    ]
                                </span>
                            </div>
                            <div
                                className={styles.replies}
                                style={{
                                    marginLeft: '3px',
                                    translate: percentage >= 1 ? '1px 3px' : '',
                                    // translate: FomoItem.god_of_wells_progress >= BigInt(10000) ? '1px 3px' : '',
                                }}
                            >
                                replies:{' '}
                                <span className={styles.amountInfoText}>
                                    0{/* {userReplies[FomoItem.token_pid.toString()]} */}
                                </span>
                            </div>
                        </div>
                    </div>
                    <div className={`${styles.ImgBox} flex flex-col gap-y-2 justify-center items-center`}>
                        <img
                            className={styles.BtmUserIcon}
                            src={FomoItem?.uri?.startsWith('http') ? FomoItem?.uri : getImgUrl(FomoItem?.uri || '')}
                        />
                        {/* {fomoInfo.swap_info.length > 0 ? (
                            <div className="text-sm font-medium">
                                {fomoInfo.swap_info.map((swapInfo, index) => {
                                    const swapName = Object.keys(swapInfo.swap)[0];
                                    return (
                                        <div key={index} className="flex w-[18px] gap-x-2 justify-center items-center">
                                            <img src={`/platform/${swapName.toLowerCase()}.svg`}></img>
                                            <div>{swapName}</div>
                                        </div>
                                    );
                                })}
                            </div>
                        ) : (
                            <div className="text-sm font-medium">
                                <div
                                    // key={index}
                                    className="flex w-[18px] gap-x-2 justify-center items-center"
                                >
                                    <img src={`/platform/icpex.svg`}></img>
                                    <div>ICPEx</div>
                                </div>
                            </div>
                        )} */}
                    </div>

                    {/* <img
                        className={styles.lowBattery}
                        style={
                            {
                                // TODO:
                                // display: lowBattery[FomoItem.token_pid.toString()] ? '' : 'none',
                            }
                        }
                        src={lowBattering}
                        alt=""
                    /> */}
                </div>
            </CardContent>
        </Card>
    );
};

export const TopTokenCard = ({
    godShowTokenInfo,
    shake,
    godOfWells,
    godOfWellReplies = 0,
}: {
    godShowTokenInfo: () => void;
    shake: boolean;
    godOfWells: TokenItem | undefined;
    godOfWellReplies?: number;
}) => {
    return (
        <Card className={`${styles.userInfoCard} ${shake ? styles.shake : ''}`} onClick={() => godShowTokenInfo()}>
            <img className={styles.subheader} src={god_img} />
            <CardContent className={`${styles.CardUserContent}`}>
                <img
                    className={styles.CardUserIcon}
                    src={godOfWells?.uri?.startsWith('http') ? godOfWells?.uri : getImgUrl(godOfWells?.uri || '')}
                />
                <div className={styles.CardInfoList}>
                    <div className={styles.CardInfoItem} style={{ cursor: 'pointer' }}>
                        created by:{' '}
                        <span
                            className={styles.godOfWellName}
                            onClick={() => {
                                window.open(`${BASE_CHAIN_EXPLORE}/address/${godOfWells?.owner}`, '_blank');
                            }}
                        >
                            {truncateString(godOfWells?.owner, 10)}
                        </span>
                    </div>
                    <div className={styles.CardInfoItem} style={{ display: 'flex', alignItems: 'center' }}>
                        <div className="text-base">
                            market cap: <span>{handleShowPrice(godOfWells?.market_cap_eth || 0, 6)} ETH</span>
                        </div>
                        <div
                            style={{
                                display: 'flex',
                                alignItems: 'center',
                                marginLeft: '5px',
                            }}
                        >
                            [<span>badge:</span>
                            <img style={{ width: '18px', height: '18px' }} src={champion}></img> ]
                        </div>
                    </div>
                    <div className={styles.CardInfoItem}>
                        replies: <span style={{ color: '#ffc900' }}>{godOfWellReplies}</span>
                    </div>
                    <div className={styles.CardInfoItem} style={{ color: '#ffc900' }}>
                        {godOfWells?.name} [ticker:{godOfWells?.symbol}]
                    </div>
                </div>
            </CardContent>
        </Card>
    );
};
