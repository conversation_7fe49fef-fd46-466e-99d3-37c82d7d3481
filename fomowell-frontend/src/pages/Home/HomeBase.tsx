import React, { useEffect, useState } from 'react';
import { Card, CardContent, Skeleton, SelectChangeEvent } from '@mui/material';
import { useNavigate } from 'react-router-dom';
import styles from './index_base.module.less';
import X from '@/assets/home/<USER>';
import buy from '@/assets/home/<USER>';
import link from '@/assets/home/<USER>';
import champion from '@/assets/home/<USER>';
import { getFomoUserInfo, get_god_of_wells, search_fomos, getBaseUserInfo } from '@/api/fomowell_launcher';
import {
    FomoProject,
    FomoProjectInfo,
    SearchParam,
    UserProfile,
} from '@/canisters/fomowell_launcher/fomowell_launcher.did';
import { Principal } from '@dfinity/principal';
import appStore from '@/store/app';
import ArrowBackIcon from '@mui/icons-material/ArrowBack';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import { get_comments_len } from '@/api/fomowell_project';
import { messageInfo } from '@/utils/appType';
import Message from '@/components/Snackbar/message';
import { getImgUrl } from '@/utils/getImgUrl';
import { formatAmountByUnit } from '@/utils/common';
import god_img from '@/assets/home/<USER>';
import Big from 'big.js';
import ICPEx from '@/assets/ICPEx.png';
import sneed from '@/assets/SNEED.png';
import { cycles as fomowell_project_cycles } from '@/api/fomowell_project';
import lowBattering from '@/assets/icpInfo/low gas.png';
import SelectsAndSearch from './components/Search';
import { TokenCard, TokenCardSkeleton, TopTokenCard, TopTokenCardSkeleton } from './components/TokenCard';
import { getCoinList, getTopCoin } from '@/api/base_api';
import { TokenItem } from '@/api/types';
interface UserInfoProps {
    //message under components/Snackbar is no longer used
    onMessageModal: (Params: messageInfo) => void;
    onCurBtnFomoInfo: (Params: FomoProject) => void;
}

const HomeBase = (props: UserInfoProps) => {
    const navigate = useNavigate();
    const [SortVal, setSortVal] = useState('BumpOrder');
    const [OrderVal, setOrderVal] = useState('DESC');
    const [searchInputVal, setSearchInputVal] = useState('');
    const [searchFomosParams, setSearchFomosParams] = useState<SearchParam>({
        order: { DESC: null },
        sort: { BumpOrder: null },
        text: '',
        limit: BigInt(6),
        start: BigInt(0),
    });
    const [showVal, setShowVal] = useState(false);
    const [fomoList, setFomoList] = useState<TokenItem[]>([]);
    const [allFomoList, setAllFomoList] = useState<TokenItem[]>([]);
    // const [userInfo, setUserInfo] = useState<Record<string, any>>({});
    // const [lowBattery, setLowBattery] = useState<Record<string, any>>({});
    // const [userReplies, setUserReplies] = useState<Record<string, string>>({});
    const [currentPage, setCurrentPage] = useState(1);
    const [pageSize] = useState(6);
    const [totalPages, setTotalPages] = useState(1);
    const [godOfWells, setGodOfWells] = useState<TokenItem>();
    const [godLoading, setGodLoading] = useState<boolean>();
    // const [godOfWellName, setGodOfWellName] = useState<string | undefined>('');
    // const [godOfWellReplies, setGodOfWellReplies] = useState<string>('');
    // const [GodUserInfo, setGodUserInfo] = useState<UserProfile>();
    // let isCurFomoListNull = false;
    // let addStart = 0;

    const handleSortMenuChange = (event: SelectChangeEvent<string>) => {
        // console.log(event.target.value, 'Sort');
        if (event.target.value == 'BumpOrder') {
            setSearchFomosParams({ ...searchFomosParams, sort: { BumpOrder: null } });
        } else if (event.target.value == 'LastReply') {
            setSearchFomosParams({ ...searchFomosParams, sort: { LastReply: null } });
        } else if (event.target.value == 'ReplyCount') {
            setSearchFomosParams({
                ...searchFomosParams,
                sort: { ReplyCount: null },
            });
        } else if (event.target.value == 'MarketCap') {
            setSearchFomosParams({ ...searchFomosParams, sort: { MarketCap: null } });
        } else if (event.target.value == 'CreationTime') {
            setSearchFomosParams({
                ...searchFomosParams,
                sort: { CreationTime: null },
            });
        }
        console.log(searchInputVal);
        setSortVal(event.target.value);
    };
    const handleOrderMenuChange = (event: SelectChangeEvent<string>) => {
        // console.log(event.target.value, 'Order');
        if (event.target.value == 'ASC') {
            setSearchFomosParams({ ...searchFomosParams, order: { ASC: null } });
        } else if (event.target.value == 'DESC') {
            setSearchFomosParams({ ...searchFomosParams, order: { DESC: null } });
        }
        setOrderVal(event.target.value);
    };
    const returnCurSort = () => {
        if (SortVal == 'BumpOrder') {
            return { BumpOrder: null };
        } else if (SortVal == 'LastReply') {
            return { LastReply: null };
        } else if (SortVal == 'ReplyCount') {
            return { ReplyCount: null };
        } else if (SortVal == 'MarketCap') {
            return { MarketCap: null };
        } else if (SortVal == 'CreationTime') {
            return { CreationTime: null };
        }
    };
    const returnCurOrder = () => {
        if (OrderVal == 'ASC') {
            return { ASC: null };
        } else if (OrderVal == 'DESC') {
            return { DESC: null };
        }
    };

    const BtnNewCoin = () => {
        navigate('/create/base');
    };

    const pagChange = async (page: number) => {
        setShowVal(false);
        const startIndex = (page - 1) * pageSize;
        const endIndex = startIndex + pageSize;

        // 从所有数据中截取当前页的数据
        const currentPageData = allFomoList.slice(startIndex, endIndex);

        if (currentPageData.length > 0) {
            setFomoList(currentPageData);
            setShowVal(true);
        } else {
            Message.warning('Nothing more');
            // 如果没有更多数据，回到上一页
            if (page > 1) {
                setCurrentPage(page - 1);
            }
        }
    };
    const handlePrevPage = () => {
        if (currentPage > 1) {
            const newPage = currentPage - 1;
            pagChange(newPage);
            setCurrentPage(newPage);
        }
    };

    const handleNextPage = () => {
        if (currentPage < totalPages) {
            const newPage = currentPage + 1;
            pagChange(newPage);
            setCurrentPage(newPage);
        } else {
            Message.warning('Nothing more');
        }
    };

    //search box query when type is search, in order to distinguish between pagination after search and pagination data by clicking the search button
    const searchFomos = async (type: string) => {
        console.log(searchInputVal);
        console.log({ ...searchFomosParams, order: returnCurOrder()!, sort: returnCurSort()!, text: searchInputVal });

        try {
            setShowVal(false);
            // 获取所有数据
            const data = await getCoinList({
                sort: SortVal.toLowerCase(),
                search: searchInputVal,
            });

            // 保存所有数据
            const allData = data.token_list || [];
            setAllFomoList(allData);

            // 计算总页数
            const pages = Math.ceil(allData.length / pageSize);
            setTotalPages(pages > 0 ? pages : 1);

            // 如果是搜索操作，重置到第一页
            if (type === 'search') {
                setCurrentPage(1);
                // 获取第一页数据
                const firstPageData = allData.slice(0, pageSize);
                setFomoList(firstPageData);
            } else {
                // 获取当前页数据
                const startIndex = (currentPage - 1) * pageSize;
                const endIndex = startIndex + pageSize;
                const currentPageData = allData.slice(startIndex, endIndex);
                setFomoList(currentPageData);
            }

            setShowVal(true);

            if (allData.length === 0) {
                Message.warning('No data found');
            }
        } catch (error) {
            console.error('Error fetching data:', error);
            Message.error('Failed to fetch data');
            setShowVal(true);
        }
    };

    useEffect(() => {
        searchFomos('');
    }, [searchFomosParams.start]);

    //The search data is triggered when the sorting criteria change
    useEffect(() => {
        searchFomos('search');
    }, [SortVal, OrderVal]);
    useEffect(() => {
        setSearchFomosParams({ ...searchFomosParams, text: searchInputVal });
    }, [searchInputVal]);
    const BtnSearch = () => {
        searchFomos('search');
    };

    const loadTopCoin = async () => {
        try {
            setGodLoading(true);
            const { kingOfTheHill: data } = await getTopCoin();

            setGodLoading(false);
            setGodOfWells(data);
        } catch (error) {
            console.debug('🚀 ~ loadTopCoin ~ error:', error);
            setGodLoading(false);
        }
    };

    useEffect(() => {
        // top
        loadTopCoin();
        // list
        searchFomos('');
    }, []);

    // top shake
    const [shake, setShake] = useState(false);
    // let CurGodFomoIdx = '';
    useEffect(() => {
        if (shake) {
            const timer = setTimeout(() => setShake(false), 500); // 500ms
            return () => clearTimeout(timer);
        }
    }, [shake]);

    // const BtnShowIcpInfo = (e: React.MouseEvent<HTMLDivElement, MouseEvent>, FomoItem: FomoProject) => {
    //     e.stopPropagation();
    //     const curParams: any = {};
    //     //Handle FomoItem data because JSON serialization only supports primitive data types
    //     for (const key in FomoItem) {
    //         if (typeof FomoItem[key as keyof FomoProject] == 'bigint') {
    //             curParams[key] = Number(FomoItem[key as keyof FomoProject]);
    //         } else if (FomoItem[key as keyof FomoProject] instanceof Principal) {
    //             curParams[key] = FomoItem[key as keyof FomoProject].toLocaleString();
    //             // console.log(curParams[key], typeof curParams[key], 111);
    //         } else {
    //             curParams[key] = FomoItem[key as keyof FomoProject].toString();
    //         }
    //     }
    //     appStore.setCurBtnFomoIcpInfo(curParams);
    //     navigate(`/${FomoItem.fomo_pid.toString()}`);
    // };
    // const godBtnShowIcpInfo = (e: React.MouseEvent<HTMLDivElement, MouseEvent>, FomoItem: FomoProject) => {
    //     e.stopPropagation();
    //     const curParams: any = {};
    //     //Handle FomoItem data because JSON serialization only supports primitive data types
    //     appStore.setCurBtnFomoIcpInfo(curParams);
    //     navigate(`/${FomoItem.fomo_pid.toString()}`);
    // };
    // const btnToProfile = (e: React.MouseEvent<HTMLSpanElement, MouseEvent>, create_user_pid: string) => {
    //     e.stopPropagation();
    //     navigate(`/profile/${create_user_pid}`);
    // };

    // //Initial display of home page data
    // const defaultGetFomoList = async () => {
    //     setShowVal(false);
    //     search_fomos(searchFomosParams).then((res) => {
    //         // console.log(res);
    //         if (res.fomo_vec) {
    //             setFomoList(res.fomo_vec);
    //             setShowVal(true);
    //             updateFomoUserNames(res.fomo_vec);
    //         }
    //     });
    //     get_god_of_wells().then((res: [] | [FomoProject]) => {
    //         if (res[0]) {
    //             getBaseUserInfo(res[0].create_user_pid.toString()).then((res) => {
    //                 setGodUserInfo(res[0]);
    //             });
    //             setGodOfWells(res[0]);
    //             getFomoUserInfo(res[0].create_user_pid)
    //                 .then((res) => {
    //                     setGodOfWellName(res[0]?.user_name);
    //                 })
    //                 .catch((e) => {
    //                     console.log(e);
    //                     Message.error('getFomoUserInfo error');
    //                 });
    //             get_comments_len(res[0].fomo_pid.toString(), res[0].create_user_pid)
    //                 .then((res) => {
    //                     if (res.toLocaleString() != '[object Object]') {
    //                         setGodOfWellReplies(res.toLocaleString());
    //                     }
    //                 })
    //                 .catch((e) => {
    //                     console.log(e);
    //                     // props.onMessageModal({ type: 'error', content: 'getData error' });
    //                     // Message.error('getData error');
    //                 });
    //         }
    //     });
    // };
    // //Asynchronously updates fomo list UserName to prevent slow loading and display [object,object]

    // const updateFomoUserNames = async (fomoVec: FomoProjectInfo[]) => {
    //     const updatedUserNames: Record<string, any> = {};
    //     const updatedUserReplies: Record<string, string> = {};
    //     const updatedLowBattery: Record<string, boolean> = {};
    //     let lowBattery = false;
    //     for (const fomoInfo of fomoVec) {
    //         const fomoItem = fomoInfo.fomo_project;
    //         const results = await Promise.allSettled([
    //             getUserFomoName(fomoItem.create_user_pid),
    //             getCommentsLen(fomoItem.fomo_pid.toString(), fomoItem.create_user_pid),
    //             fomowell_project_cycles(fomoItem.fomo_pid.toString()),
    //         ]);

    //         const userName = results[0].status === 'fulfilled' ? results[0].value : '';
    //         const userReplied = results[1].status === 'fulfilled' ? results[1].value : '';
    //         const fomowell_cycles = results[2].status === 'fulfilled' ? results[2].value : 0;
    //         if (
    //             new Big(Number(fomowell_cycles)).lt(new Big(100000000000)) ||
    //             (results[2].status === 'rejected' && results[2].reason.result.reject_message.includes('frozen'))
    //         ) {
    //             lowBattery = true;
    //         } else {
    //             lowBattery = false;
    //         }
    //         updatedUserNames[fomoItem.create_user_pid.toString()] = userName!;
    //         updatedLowBattery[fomoItem.token_pid.toString()] = lowBattery;
    //         if (userReplied != '[object Object]') {
    //             // console.log(fomoItem.token_pid.toString());
    //             updatedUserReplies[fomoItem.token_pid.toString()] = userReplied;
    //             setUserReplies(updatedUserReplies);
    //         }
    //     }
    //     // console.log(updatedUserNames);
    //     setUserInfo(updatedUserNames);
    //     setLowBattery(updatedLowBattery);
    // };
    // const getUserFomoName = async (pid: Principal) => {
    //     try {
    //         const name = await getFomoUserInfo(pid);
    //         return name[0] ? name[0] : '';
    //     } catch (error) {
    //         console.log(error);
    //         // props.onMessageModal({ type: 'error', content: 'getFomoUserInfo error' });
    //         Message.error('getFomoUserInfo error');
    //     }
    // };
    // const getCommentsLen = async (fomo_pid: string, create_user_pid: Principal) => {
    //     const userReplied = await get_comments_len(fomo_pid, create_user_pid);
    //     return userReplied.valueOf().toLocaleString();
    // };

    // const BtnToUserInfoPage = (e: React.MouseEvent<HTMLSpanElement, MouseEvent>, Pid: Principal) => {
    //     e.stopPropagation();
    //     localStorage.setItem('setCurUserInfoPid', Pid.toString());
    //     navigate(`/profile/${Pid.toString()}`);
    // };

    // useEffect(() => {
    //     defaultGetFomoList();
    //     setTimeout(() => {
    //         setInterval(() => {
    //             get_god_of_wells().then((res: [] | [FomoProject]) => {
    //                 // setShake(false);
    //                 if (res[0]) {
    //                     getBaseUserInfo(res[0].create_user_pid.toString()).then((res) => {
    //                         setGodUserInfo(res[0]);
    //                     });
    //                     setGodOfWells(res[0]);
    //                     if (String(res[0].fomo_idx) == CurGodFomoIdx) {
    //                         setShake(false);
    //                     } else {
    //                         setShake(true);
    //                         CurGodFomoIdx = String(res[0].fomo_idx);
    //                     }
    //                     getFomoUserInfo(res[0].create_user_pid)
    //                         .then((res) => {
    //                             setGodOfWellName(res[0]?.user_name);
    //                         })
    //                         .catch((e) => {
    //                             console.log(e);
    //                             Message.error('getFomoUserInfo error');
    //                         });
    //                     get_comments_len(res[0].fomo_pid.toString(), res[0].create_user_pid)
    //                         .then((res) => {
    //                             if (res.toLocaleString() != '[object Object]') {
    //                                 setGodOfWellReplies(res.toLocaleString());
    //                             }
    //                         })
    //                         .catch((e) => {
    //                             console.log(e);
    //                             // props.onMessageModal({ type: 'error', content: 'getData error' });
    //                             // Message.error('getData error');
    //                         });
    //                 }
    //             });
    //         }, 10000);
    //     }, 20000);
    // }, []);

    return (
        <div className={styles.HomeMain}>
            <div className={styles.HomeCenter}>
                <div className={styles.MainTitle} onClick={BtnNewCoin}>
                    Start a new coin
                </div>
                {/* TODO: top card */}
                {!godLoading && godOfWells ? (
                    <TopTokenCard
                        godShowTokenInfo={() => {
                            const newUrl = `/details/${godOfWells?.erc20TokenAddress}/${godOfWells?.token}?symbol=${godOfWells?.symbol}`;
                            navigate(newUrl, {
                                state: {
                                    ercAddress: godOfWells?.erc20TokenAddress,
                                    tokenAddress: godOfWells?.token,
                                    symbol: godOfWells?.symbol,
                                },
                            });
                        }}
                        shake={shake}
                        godOfWells={godOfWells}
                        godOfWellReplies={0}
                    />
                ) : godLoading ? (
                    <TopTokenCardSkeleton />
                ) : null}
            </div>
            <div className={styles.HomeBottom}>
                <SelectsAndSearch
                    handleSortMenuChange={handleSortMenuChange}
                    handleOrderMenuChange={handleOrderMenuChange}
                    BtnSearch={BtnSearch}
                    setSearchInputVal={setSearchInputVal}
                />

                <div className={styles.BottomCard}>
                    {showVal && fomoList.length == 0 && (
                        <div className="mt-10 w-full text-base text-white">No Fomo</div>
                    )}
                    {showVal &&
                        fomoList.map((fomoInfo) => {
                            return (
                                <TokenCard
                                    key={fomoInfo._id}
                                    FomoItem={fomoInfo}
                                    showTokenInfo={() => {
                                        const newUrl = `/details/${fomoInfo?.erc20TokenAddress}/${fomoInfo?.token}`;
                                        navigate(newUrl, {
                                            state: {
                                                ercAddress: fomoInfo?.erc20TokenAddress,
                                                tokenAddress: fomoInfo?.token,
                                                symbol: fomoInfo?.symbol,
                                            },
                                        });
                                    }}
                                />
                            );
                        })}
                    {!showVal &&
                        [1, 2, 3, 4, 5, 6].map((item, index) => {
                            return <TokenCardSkeleton key={index} />;
                        })}
                </div>
                {showVal && fomoList.length > 0 && (
                    <div
                        style={{
                            marginTop: '10px',
                            translate: '-13px',
                            paddingBottom: '10px',
                            display: 'flex',
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}
                    >
                        <ArrowBackIcon
                            onClick={handlePrevPage}
                            sx={{
                                color: currentPage > 1 ? '#fff' : '#666',
                                marginRight: '10px',
                                cursor: currentPage > 1 ? 'pointer' : 'not-allowed',
                            }}
                        ></ArrowBackIcon>
                        <div style={{ color: '#fff' }}>
                            {currentPage} / {totalPages}
                        </div>
                        <ArrowForwardIcon
                            onClick={handleNextPage}
                            sx={{
                                color: currentPage < totalPages ? '#fff' : '#666',
                                marginLeft: '10px',
                                cursor: currentPage < totalPages ? 'pointer' : 'not-allowed',
                            }}
                        ></ArrowForwardIcon>
                    </div>
                )}
            </div>
        </div>
    );
};

export default HomeBase;
