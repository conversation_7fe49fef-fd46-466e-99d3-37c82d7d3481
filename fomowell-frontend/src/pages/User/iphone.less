@media (max-width:500px) and (min-width:401px) {
  .UserInfoMain {
    margin-top: 55px;

    .UserInfoHeader {
      width: 400px;

      .HeaderTop {
        .HeaderLeft {
          .UserIcon {
            width: 40px;
            // height: 40px;
          }

          .UserName {
            font-size: 15px;
          }
        }

        .HeaderRight {
          font-size: 15px;
        }
      }
    }

    .UserInfoBottom {
      width: 100%;

      .CoinCreatedCard {
        .Content {
          cursor: pointer;

          .LeftImg {
            width: 90px;
            height: 90px;
          }

          .RightContent {
            .headerFrom {
              font-size: 15px;
            }

            .MarketCap {
              font-size: 15px;
            }

            .replies {
              font-size: 15px;
            }

            .repliesContent {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

@media (max-width:400px) {
  .UserInfoMain {
    margin-top: 55px;

    .UserInfoHeader {
      width: 100%;

      .HeaderTop {
        .HeaderLeft {
          .UserIcon {
            width: 40px;
            // height: 40px;
          }

          .UserName {
            font-size: 15px;
          }
        }

        .HeaderRight {
          font-size: 15px;
        }
      }

      .HeaderBottom {
        display: flex;
        flex-direction: column;
        align-items: center;
        // justify-content: space-between;
        margin-top: 15px;
        background: #232634;
        border-radius: 8px;
        // width: 500px;
        padding: 8px 5px;

        .UserID:nth-child(2) {
          margin-left: 0;
        }

        .UserID {
          display: flex;
          font-size: 14px;
          color: #FFFFFF;
          letter-spacing: 0;
          font-weight: 500;

          margin-top: 5px;

          .IconImg {
            color: #5d52e0;
            width: 18px;
            height: 18px;
            margin-left: 10px;
            cursor: pointer;
          }
        }
      }
    }

    .UserInfoBottom {
      width: 100%;

      .CoinCreatedCard {
        .Content {
          cursor: pointer;

          .LeftImg {
            width: 90px;
            height: 90px;
          }

          .RightContent {
            .headerFrom {
              font-size: 15px;
            }

            .MarketCap {
              font-size: 15px;
            }

            .replies {
              font-size: 15px;
            }

            .repliesContent {
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}