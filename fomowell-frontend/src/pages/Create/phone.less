@media (max-width:900px) and (min-width:401px) {
  .CoinFrom {
    caret-color: #7F7F7F;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: 50%;
    padding-bottom: 20px;
    transform: translate(-50%);
    margin-top: 48px;

    input::placeholder {
      font-size: 20px;
      color: #9EBADF;
      letter-spacing: 0;
      font-weight: 400;
    }

    .inputCoin {
      border: 0px solid;
      border-radius: 8px;
      padding-left: 8px;
      width: 400px;
      height: 48px;
      color: #676767;
      background: #232634;
    }

    .LabelCom {
      font-family: '';
      font-size: 20px;
      color: #FFFFFF;
      letter-spacing: 0;
      font-weight: 600;
    }

    .CoinName {
      margin-top: 30px;
    }

    .CoinTicker {
      margin-top: 30px;
    }

    .CoinDescription {
      margin-top: 30px;

      .inputCoin {
        height: 121px;
      }
    }

    .selfButtom {
      display: flex;
    }

    .ShowMore {
      font-family: '';
      font-size: 20px;
      color: #328BFF;
      letter-spacing: 0;
      font-weight: 600;
      margin-top: 20px;
      cursor: pointer;

      .arrow {
        height: 24px;
        width: 24px;
        transform: rotate(180deg) translateY(-5px) translateX(-5px);
      }
    }

    .CreateFomo {
      background-image: linear-gradient(270deg, #A25FFF 0%, #6931FF 100%);
      border-radius: 4px;
      height: 55px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-top: 20px;
      cursor: pointer;
      width: 400px;
      // padding: 5px 0 5px 0;
      text-transform: none;

      .top {
        font-family: '';
        font-size: 18px;
        // color: #FFFFFF;
        // letter-spacing: 0;
        // text-align: center;
        font-weight: 600;
        margin-top: 5px;
      }

      .buttom {
        font-family: '';
        font-size: 16px;
        // color: #FFFFFF;
        letter-spacing: 0;
        // text-align: center;
        font-weight: 600;
        margin-bottom: 5px;
      }
    }

    .showmoreFrom {
      .twitterLink {
        margin-top: 20px;
      }

      .telegramLink {
        margin-top: 20px;
      }

      .website {
        margin-top: 20px;
      }
    }

    .locked {
      margin-top: 10px;

      .lockedVal {
        width: 60px;
        background-color: #242633;
        border-radius: 8px;
        padding: 0 6px;
        color: #fff;
      }

      .optionGroup {
        margin-top: 5px;
      }

      .lockedDesc {
        color: #6DA6DA;
        font-size: 14px;
        font-weight: 600;
      }
    }
  }
}

@media (max-width:400px) {
  .CoinFrom {
    caret-color: #7F7F7F;
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: 50%;
    padding-bottom: 20px;
    transform: translate(-50%);
    margin-top: 48px;

    input::placeholder {
      font-size: 20px;
      color: #9EBADF;
      letter-spacing: 0;
      font-weight: 400;
    }

    .inputCoin {
      border: 0px solid;
      border-radius: 8px;
      padding-left: 8px;
      height: 48px;
      width: 100%;
      color: #676767;
      background: #232634;
    }

    .LabelCom {
      font-family: '';
      font-size: 20px;
      color: #FFFFFF;
      letter-spacing: 0;
      font-weight: 600;
    }

    .CoinName {
      margin-top: 30px;
      width: 100%;
    }

    .CoinTicker {
      margin-top: 30px;
      width: 100%;
    }

    .CoinDescription {
      margin-top: 30px;
      width: 100%;

      .inputCoin {
        height: 121px;
      }
    }

    .selfButtom {
      display: flex;
    }

    .ShowMore {
      font-family: '';
      font-size: 20px;
      color: #328BFF;
      letter-spacing: 0;
      font-weight: 600;
      margin-top: 20px;
      cursor: pointer;

      .arrow {
        height: 24px;
        width: 24px;
        transform: rotate(180deg) translateY(-5px) translateX(-5px);
      }
    }

    .CreateFomo {
      background-image: linear-gradient(270deg, #A25FFF 0%, #6931FF 100%);
      border-radius: 4px;
      height: 55px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-top: 20px;
      cursor: pointer;
      width: 100%;
      padding: 5px 0 5px 0;
      text-transform: none;

      .top {
        font-family: '';
        font-size: 16px;
        // color: #FFFFFF;
        letter-spacing: 0;
        text-align: center;
        font-weight: 600;
      }

      .buttom {
        font-family: '';
        font-size: 16px;
        // color: #FFFFFF;
        letter-spacing: 0;
        text-align: center;
        font-weight: 600;
      }
    }

    .showmoreFrom {
      width: 100%;

      .twitterLink {
        margin-top: 20px;
      }

      .telegramLink {
        margin-top: 20px;
      }

      .website {
        margin-top: 20px;
      }
    }

    .locked {
      margin-top: 10px;

      .lockedVal {
        width: 60px;
        background-color: #242633;
        border-radius: 8px;
        padding: 0 6px;
        color: #fff;
      }

      .optionGroup {
        margin-top: 5px;
      }

      .lockedDesc {
        color: #6DA6DA;
        font-size: 14px;
        font-weight: 600;
      }
    }

  }
}