import { Box, Modal, CircularProgress, Fade } from '@mui/material';
import CheckIcon from '@mui/icons-material/Check';
import success from '@/assets/icpInfo/success.png';
import pending from '@/assets/icpInfo/pending.png';
import CloseIcon from '@mui/icons-material/Close';

const ModalStyle = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    backgroundColor: '#262939',
    borderRadius: '8px',
    borderColor: '#262939',
    pt: 2,
    px: 4,
    pb: 3,
};

const StepModal = ({
    openStep,
    handleCloseStep,
    setOpenStep,
    isStepApprove,
    StepApproveSuccess,
    isStepCreateFomo,
    StepCreateFomoSuccess,
}: {
    openStep: boolean;
    handleCloseStep: () => void;
    setOpenStep: (val: boolean) => void;
    isStepApprove: boolean;
    StepApproveSuccess: boolean;
    isStepCreateFomo: boolean;
    StepCreateFomoSuccess: boolean;
}) => {
    return (
        <Modal open={openStep} onClose={handleCloseStep} style={{ borderColor: '#262939' }}>
            <Fade
                in={openStep}
                style={{
                    position: 'relative',
                    width: '300px',
                    outline: 'none',
                }}
            >
                <Box
                    sx={{
                        ...ModalStyle,
                        alignItems: 'center',
                        display: 'flex',
                        flexDirection: 'column',
                    }}
                >
                    <div onClick={() => setOpenStep(false)}>
                        <CloseIcon
                            sx={{
                                color: '#fff',
                                position: 'absolute',
                                right: '10px',
                                top: '8px',
                                cursor: 'pointer',
                            }}
                        ></CloseIcon>
                    </div>
                    <div
                        className="header"
                        style={{
                            display: 'flex',
                            flexDirection: 'column',
                            justifyContent: 'center',
                            alignItems: 'center',
                        }}
                    >
                        <div className="Title" style={{ fontSize: '24px', color: '#fff' }}>
                            Create fomo in progress
                        </div>
                        <div className="Title" style={{ fontSize: '12px', color: '#fff', marginTop: '5px' }}>
                            Please wait some time for transactions to finish
                        </div>
                    </div>
                    <div
                        style={{
                            position: 'relative',
                            display: 'flex',
                            justifyContent: 'space-around',
                            alignItems: 'center',
                            marginTop: '15px',
                        }}
                    >
                        <div
                            style={{
                                width: '150px',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}
                        >
                            <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                                <CircularProgress
                                    thickness={isStepApprove ? 2 : 0}
                                    size={60}
                                    sx={{
                                        color: '#fff',
                                        background: '#746cec',
                                        borderRadius: '50%',
                                    }}
                                />
                                <Box
                                    sx={{
                                        top: 0,
                                        left: 0,
                                        bottom: 0,
                                        right: 0,
                                        position: 'absolute',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <img
                                        src={success}
                                        style={{
                                            height: '15px',
                                            position: 'absolute',
                                            right: '3px',
                                            top: 0,
                                            visibility: StepApproveSuccess ? 'visible' : 'hidden',
                                        }}
                                    />
                                    {StepApproveSuccess ? (
                                        <CheckIcon sx={{ color: '#fff' }}></CheckIcon>
                                    ) : (
                                        <CheckIcon sx={{ color: '#fff' }}></CheckIcon>
                                        // <img style={{ height: '20px' }} src={pending} />
                                    )}
                                </Box>
                            </Box>
                            <div
                                style={{
                                    textAlign: 'center',
                                    color: '#fff',
                                    fontSize: '14px',
                                }}
                            >
                                Approve Base
                            </div>
                        </div>
                        <div style={{ color: '#fff' }}>{`>`}</div>
                        <div
                            style={{
                                width: '150px',
                                display: 'flex',
                                flexDirection: 'column',
                                justifyContent: 'center',
                                alignItems: 'center',
                            }}
                        >
                            <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                                <CircularProgress
                                    thickness={isStepCreateFomo ? 2 : 0}
                                    size={60}
                                    sx={{
                                        color: '#fff',
                                        background: '#746cec',
                                        borderRadius: '50%',
                                    }}
                                />
                                <Box
                                    sx={{
                                        top: 0,
                                        left: 0,
                                        bottom: 0,
                                        right: 0,
                                        position: 'absolute',
                                        display: 'flex',
                                        alignItems: 'center',
                                        justifyContent: 'center',
                                    }}
                                >
                                    <img
                                        src={success}
                                        style={{
                                            height: '15px',
                                            position: 'absolute',
                                            right: '3px',
                                            top: 0,
                                            visibility: StepCreateFomoSuccess ? 'visible' : 'hidden',
                                        }}
                                    />
                                    {StepCreateFomoSuccess ? (
                                        <CheckIcon sx={{ color: '#fff' }}></CheckIcon>
                                    ) : (
                                        <img src={pending} style={{ height: '20px' }}></img>
                                    )}
                                </Box>
                            </Box>
                            <div
                                style={{
                                    textAlign: 'center',
                                    color: '#fff',
                                    fontSize: '14px',
                                }}
                            >
                                Create Fomo
                            </div>
                        </div>
                    </div>
                </Box>
            </Fade>
        </Modal>
    );
};
export default StepModal;
