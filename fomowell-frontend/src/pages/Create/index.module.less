@media (min-width:900px) {
  .CoinFrom {
    caret-color: #7F7F7F;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    margin-left: 50%;
    padding-bottom: 20px;
    transform: translate(-50%);
    margin-top: 81px;
    aspect-ratio: '';

    .UploadBtn {
      .MuiButtonBase-root {
        aspect-ratio: 0
      }
    }

    input::placeholder {
      font-size: 20px;
      color: #9EBADF;
      letter-spacing: 0;
      font-weight: 400;
    }

    .inputCoin {
      border: 0px solid;
      border-radius: 8px;
      padding-left: 8px;
      width: 581px;
      height: 48px;
      font-size: 20px;
      color: #676767;
      background: #232634;
    }

    .LabelCom {
      font-family: '';
      font-size: 20px;
      color: #FFFFFF;
      letter-spacing: 0;
      font-weight: 600;
    }

    .CoinName {
      margin-top: 20px;
    }

    .CoinTicker {
      margin-top: 20px;
    }

    .CoinDescription {
      margin-top: 20px;

      .inputCoin {
        height: auto;
      }
    }

    .selfButtom {
      display: flex;
    }

    .ShowMore {
      font-family: '';
      font-size: 20px;
      color: #328BFF;
      letter-spacing: 0;
      font-weight: 600;
      margin-top: 15px;
      cursor: pointer;

      .arrow {
        height: 24px;
        width: 24px;
        transform: rotate(180deg) translateY(-5px) translateX(-5px);
      }
    }

    .CreateFomo {
      background-image: linear-gradient(270deg, #A25FFF 0%, #6931FF 100%);
      border-radius: 4px;
      height: 70px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin-top: 20px;
      cursor: pointer;
      width: 583px;
      text-transform: none;

      .top {
        font-family: '';
        font-size: 20px;
        // color: #FFFFFF;
        letter-spacing: 0;
        text-align: center;
        font-weight: 600;
        text-transform: none;
      }

      .buttom {
        font-family: '';
        font-size: 16px;
        // color: #FFFFFF;
        letter-spacing: 0;
        text-align: center;
        font-weight: 600;
      }

    }

    .showMoreFrom {
      .twitterLink {
        margin-top: 20px;
      }

      .telegramLink {
        margin-top: 20px;
      }

      .website {
        margin-top: 20px;
      }
    }

    .locked {
      margin-top: 20px;

      .lockedVal {
        width: 60px;
        background-color: #242633;
        border-radius: 8px;
        padding: 0 6px;
        color: #fff;
      }

      .optionGroup {
        margin-top: 5px;
      }

      .lockedDesc {
        width: 560px;
        display: '';
        color: #6DA6DA;
        font-size: 14px;
        font-weight: 600;
      }
    }

  }
}

@import url(./phone.less);