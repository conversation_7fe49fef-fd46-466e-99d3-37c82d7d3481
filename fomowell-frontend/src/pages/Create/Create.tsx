import { Box, InputBase, InputLabel, Input, IconButton, CircularProgress } from '@mui/material';
import styles from './index.module.less';
import chooseFile from '@/assets/home/<USER>';
import arrowUp from '@/assets/home/<USER>';
import React, { useState } from 'react';
import { FomoProjectCreate } from '@/canisters/fomowell_launcher/fomowell_launcher.did';
import appStore from '@/store/app';
import { messageInfo } from '@/utils/appType';
import CloseIcon from '@mui/icons-material/Close';
import Message from '@/components/Snackbar/message';
import { QeqImg } from '@/api/img_request';
import { isValidHttpUrl } from '@/utils/httpValid';
import { observer } from 'mobx-react-lite';
import { useNavigate } from 'react-router-dom';
import { LoadingButton } from '@mui/lab';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import { useWalletInfo } from '@/hooks/useWalletInfo';
import { BASE_CHAIN_ID, getPreSetAmounts, getSymbol } from '@/api/initWallet';
import StepModal from './StepModal';
import { useBalance, useConnect } from 'wagmi';
import { BuildTransaction, TransactionType } from '@/api/types';
import { buildTransaction, createToken, uploadFile } from '@/api/base_api';

interface UserInfoProps {
    //message under components/Snackbar is no longer used
    onRouteChange: (Params: messageInfo) => void;
    onWellModal: (Param: boolean) => void;
    openSelectWell: boolean;
}

const Create: React.FC<UserInfoProps> = observer(() => {
    const navigate = useNavigate();
    const preSetAmounts = getPreSetAmounts();

    const { address, isConnected } = useWalletInfo();
    const { connect, connectors } = useConnect();

    const { data: userEthBalance, error } = useBalance({
        address, // 使用当前钱包地址
        chainId: BASE_CHAIN_ID,
    });

    const connectWallet = () => {
        if (connectors.length > 0) {
            connect({ connector: connectors[connectors.length - 1] });
        }
    };

    const [ImgFile, setImgFile] = useState<File>();
    const [fileName, setFileName] = useState('No file chosen');

    const BtnUpPhoto = async (e: React.ChangeEvent<HTMLInputElement>) => {
        if (!e.target.files) {
            Message.error('file error');
            return;
        }
        if (e.target.files[0].size > 5 * 1024 * 1024) {
            Message.error('Image must smaller than 5MB! and You can only upload JPG/PNG/GIF file!');
            return;
        } else if (
            !['JPG', 'PNG', 'GIF', 'image/jpeg', 'image/png', 'image/gif', 'image/jpg'].includes(e.target.files[0].type)
        ) {
            Message.error('Image must smaller than 5MB! and You can only upload JPG/PNG/GIF file!');
            return;
        }
        const reader: any = new FileReader();

        console.log('e.target.files', e.target.files);
        reader.readAsDataURL(e.target.files[0]);
        reader.onload = function () {
            const newUrl = this.result;
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            const img = new Image();
            img.src = newUrl;
            let data = '';
            img.onload = function () {
                if (!e.target.files) {
                    Message.error('file error');
                    return;
                }
                // let width = img.width;
                canvas.width = 80;
                canvas.height = 80;
                ctx?.drawImage(img, 0, 0, canvas.width, canvas.height);
                // Convert to base64 quality to image compression quality. The smaller the value between 0 and 1, the larger the compression, the poorer the image quality
                data = canvas.toDataURL(e.target.files[0].type, 0.3);
                setFomoParams({ ...FomoParams, logo: data, img_url: data });
                setFileName(e.target?.files[0].name);
            };
        };
        setImgFile(e.target.files[0]);
        return () => {};
    };

    const [FomoParams, setFomoParams] = useState<FomoProjectCreate>({
        dogmi_dao_lock: [],
        sneed_dao_lock: [],
        name: '',
        ticker: '',
        img_url: '',
        logo: '',
        description: '',
        twitter_link: '',
        telegram_link: '',
        website: '',
        amount: 0.0,
    });

    const requiredInfo: (keyof FomoProjectCreate)[] = ['name', 'ticker', 'img_url', 'description', 'amount'];
    const ValidParams: (keyof FomoProjectCreate)[] = ['twitter_link', 'telegram_link', 'website'];

    const [isShowMore, setIsShowMore] = useState('none');
    const [openStep, setOpenStep] = useState(false);
    const [isStepApprove, setIsStepApprove] = useState(true);
    const [isStepCreateFomo, setIsStepCreateFomo] = useState(false);
    const [StepApproveSuccess, setStepApproveSuccess] = useState(false);
    const [StepCreateFomoSuccess, setStepCreateFomoSuccess] = useState(false);
    const [openRightBox, setOpenRightBox] = useState(false);

    const handleCloseStep = () => {
        setOpenStep(false);
    };

    const BtnShowMore = () => {
        isShowMore == 'none' ? setIsShowMore('') : setIsShowMore('none');
    };

    const btnFomo = async () => {
        if (!address || !isConnected) {
            Message.error('Please connect wallet first!');
            connectWallet();
            return;
        }

        for (const key of ValidParams) {
            if (FomoParams[key] && !isValidHttpUrl(FomoParams[key] as any)) {
                Message.error(`Please enter the correct ${key}`);
                return;
            }
        }
        try {
            for (const key of requiredInfo) {
                if (!FomoParams[key]) {
                    if (key == 'img_url') {
                        Message.error(`Please enter your fomo image`);
                    } else {
                        Message.error(`Please enter your fomo ${key}`);
                    }
                    return;
                }
            }

            if (!FomoParams.amount) return Message.error('Please enter mint amount first!');

            if (FomoParams.amount <= 0.002) return Message.error('mint amount must greater than 0.002!');

            if (Number(userEthBalance?.formatted) < FomoParams.amount) {
                Message.error('Insufficient balance');
                return;
            }

            setFomoParams({ ...FomoParams });
            setOpenStep(true);

            const result = await uploadFile(ImgFile!);
            // console.log('🚀 ~ BtnUpPhoto ~ result:', result);
            // const res = await QeqImg(ImgFile!);
            // console.log('🚀 ~ btnFomo ~ res:', res);
            setFomoParams((prev) => {
                return { ...prev, img_url: result };
            });

            stepApprove(result);
        } catch (error) {
            Message.error('Image upload error');
            setOpenStep(false);
        }
    };

    const stepApprove = async (reference: string) => {
        setOpenRightBox(true);

        try {
            const payload: BuildTransaction = {
                from_address: address,
                token_icon: reference,
                token_name: FomoParams.name,
                token_symbol: FomoParams.ticker,
                token_description: FomoParams.description,
                mint_fee: (FomoParams.amount || 0).toString(),
                telegram_link: FomoParams.telegram_link,
                twitter_link: FomoParams.twitter_link,
                website: FomoParams.website,
            };

            const transaction: TransactionType = await buildTransaction(payload);

            if (!transaction) {
                setOpenStep(false);
                setOpenRightBox(false);
                Message.error('Create fomo failed');
                return;
            }
            setStepApproveSuccess(true);
            StepCreateFomo(transaction);
        } catch (error) {
            setOpenRightBox(false);
            setOpenStep(false);
            Message.error('Create fomo failed');
        }
    };

    const StepCreateFomo = async (transaction: TransactionType) => {
        setOpenRightBox(true);
        setIsStepCreateFomo(true);

        try {
            // submit create
            const result = await createToken(transaction);

            setIsStepCreateFomo(false);
            setStepCreateFomoSuccess(true);

            Message.success('Create fomo success!');
            setOpenRightBox(false);
            setTimeout(() => {
                navigate(`/board/base`);
            }, 2000);
            setTimeout(() => {
                setStepCreateFomoSuccess(false);
                setOpenStep(false);
                setIsStepApprove(true);
                setIsStepCreateFomo(false);
                setStepApproveSuccess(false);
            }, 800);
        } catch (e) {
            console.log(e);
            Message.error('Create fomo failed');
            setOpenRightBox(false);
            setStepCreateFomoSuccess(false);
            setTimeout(() => {
                setOpenStep(false);
                setIsStepApprove(true);
                setIsStepCreateFomo(false);
                setStepApproveSuccess(false);
            }, 800);
        }
    };

    return (
        <div>
            <Box className={styles.CoinFrom}>
                <div style={{ color: '#fff', cursor: 'pointer', paddingTop: '10px' }} onClick={() => navigate(-1)}>
                    [ go back ]
                </div>
                <form>
                    <div className={styles.CoinName}>
                        <InputLabel required className={styles.LabelCom}>
                            name
                        </InputLabel>
                        <InputBase
                            className={styles.inputCoin}
                            onChange={(e) => setFomoParams({ ...FomoParams, name: e.target.value })}
                            // required
                        ></InputBase>
                    </div>
                    <div className={styles.CoinTicker}>
                        <InputLabel required className={styles.LabelCom}>
                            ticker
                        </InputLabel>
                        <InputBase
                            className={styles.inputCoin}
                            onChange={(e) => setFomoParams({ ...FomoParams, ticker: e.target.value })}
                            // required
                        ></InputBase>
                    </div>
                    <div className={styles.CoinDescription}>
                        <InputLabel required className={styles.LabelCom}>
                            description
                        </InputLabel>
                        <InputBase
                            rows="5"
                            multiline
                            className={styles.inputCoin}
                            sx={{
                                '.css-3b6ca1-MuiInputBase-input': {
                                    border: '1px solid red',
                                },
                            }}
                            onChange={(e) => setFomoParams({ ...FomoParams, description: e.target.value })}
                            // required
                        ></InputBase>
                    </div>
                    <div className={styles.CoinTicker}>
                        <InputLabel required className={styles.LabelCom}>
                            image
                        </InputLabel>
                        <InputBase
                            value={fileName}
                            required
                            style={{ color: '#9EBADF' }}
                            onChange={(e) => setFomoParams({ ...FomoParams, img_url: e.target.value })}
                            sx={{
                                '.MuiInputBase-inputAdornedStart': {
                                    aspectRatio: '0',
                                },
                            }}
                            startAdornment={
                                <label htmlFor="icon-button-file-SetCoin" className={styles.UploadBtn}>
                                    <Input
                                        id="icon-button-file-SetCoin"
                                        type="file"
                                        style={{ display: 'none', height: '48px' }}
                                        onChange={BtnUpPhoto}
                                    />
                                    <IconButton
                                        color="primary"
                                        aria-label="upload picture"
                                        component="div"
                                        style={{ aspectRatio: '0' }}
                                    >
                                        <img src={chooseFile} style={{ cursor: 'pointer' }}></img>
                                    </IconButton>
                                </label>
                            }
                            className={styles.inputCoin}
                        ></InputBase>
                    </div>
                    <div className={styles.CoinTicker}>
                        <InputLabel required className={styles.LabelCom}>
                            How many {FomoParams.ticker && `$${FomoParams.ticker}`} you want to buy
                        </InputLabel>
                        <InputBase
                            value={FomoParams.amount}
                            className={styles.inputCoin}
                            type="number"
                            onChange={(e) => {
                                const value: number = parseFloat(e.target.value);
                                setFomoParams({ ...FomoParams, amount: value });
                            }}
                            endAdornment={<div className="pr-3">{getSymbol()}</div>}
                            // required
                        ></InputBase>

                        <div className="flex flex-wrap gap-2 justify-start items-center mt-2">
                            {preSetAmounts.map((item) => (
                                <div
                                    key={item.label}
                                    onClick={() => {
                                        setFomoParams({ ...FomoParams, amount: item.value });
                                    }}
                                    className="cursor-pointer flex items-center justify-center px-2 h-10 min-h-10 border-none bg-[#02161C] text-white hover:opacity-60 rounded-lg"
                                >
                                    {item.label}
                                </div>
                            ))}
                        </div>
                    </div>

                    <div className={styles.ShowMore} onClick={BtnShowMore}>
                        {isShowMore ? 'Show' : 'Hide'} more options
                        <img
                            className={styles.arrow}
                            src={arrowUp}
                            style={{
                                rotate: isShowMore == 'none' ? '0deg' : '-180deg',
                                translate: isShowMore == 'none' ? '0px' : '10px 10px',
                            }}
                        ></img>
                    </div>
                    <div className={styles.showMoreFrom} style={{ display: isShowMore }}>
                        <div className={styles.twitterLink}>
                            <InputLabel className={styles.LabelCom}>twitter link</InputLabel>
                            <InputBase
                                className={styles.inputCoin}
                                type="url"
                                placeholder="(optional)"
                                onChange={(e) => setFomoParams({ ...FomoParams, twitter_link: e.target.value })}
                            ></InputBase>
                        </div>
                        <div className={styles.telegramLink}>
                            <InputLabel className={styles.LabelCom}>telegram link</InputLabel>
                            <InputBase
                                className={styles.inputCoin}
                                placeholder="(optional)"
                                onChange={(e) => setFomoParams({ ...FomoParams, telegram_link: e.target.value })}
                            ></InputBase>
                        </div>
                        <div className={styles.website}>
                            <InputLabel className={styles.LabelCom}>website</InputLabel>
                            <InputBase
                                className={styles.inputCoin}
                                placeholder="(optional)"
                                onChange={(e) => setFomoParams({ ...FomoParams, website: e.target.value })}
                            ></InputBase>
                        </div>
                    </div>
                    <LoadingButton
                        id="CreateFomo"
                        loading={openRightBox}
                        className={styles.CreateFomo}
                        sx={{ color: '#fff' }}
                        onClick={btnFomo}
                    >
                        <div className={styles.top}>{isConnected ? 'Create Fomo' : 'Connect Wallet'}</div>
                    </LoadingButton>
                </form>
            </Box>

            <div
                style={{
                    position: 'fixed',
                    top: '120px',
                    right: '10px',
                    display: openRightBox ? '' : 'none',
                }}
            >
                <div onClick={() => setOpenRightBox(false)} style={{ position: 'absolute', right: '10px', top: '5px' }}>
                    <CloseIcon sx={{ color: '#fff', cursor: 'pointer' }}></CloseIcon>
                </div>
                <div
                    style={{
                        padding: '10px 10px 10px 10px',
                        backgroundColor: '#1f2946',
                        minWidth: '240px',
                        maxWidth: '400px',
                        borderRadius: '15px',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'start',
                    }}
                >
                    <div>
                        <div style={{ position: 'relative' }}>
                            <CircularProgress
                                thickness={2}
                                size={50}
                                sx={{
                                    color: '#fff',
                                    background: '#746cec',
                                    borderRadius: '50%',
                                }}
                            />
                            <AccessTimeIcon
                                sx={{
                                    color: '#fff',
                                    position: 'absolute',
                                    top: '9.5px',
                                    right: '10.5px',
                                    fontSize: '30px',
                                }}
                            ></AccessTimeIcon>
                        </div>
                    </div>
                    <div
                        style={{
                            marginLeft: '10px',
                            marginRight: '25px',
                            maxWidth: '320px',
                        }}
                    >
                        <div style={{ color: '#fff' }}>Create fomo {FomoParams.ticker}</div>
                        <div style={{ color: '#5f56bf', cursor: 'pointer' }} onClick={() => setOpenStep(true)}>
                            View progress
                        </div>
                    </div>
                </div>
            </div>

            {/* step loading model */}
            <StepModal
                openStep={openStep}
                StepApproveSuccess={StepApproveSuccess}
                StepCreateFomoSuccess={StepCreateFomoSuccess}
                isStepApprove={isStepApprove}
                isStepCreateFomo={isStepCreateFomo}
                handleCloseStep={handleCloseStep}
                setOpenStep={setOpenStep}
            />
        </div>
    );
});

export default Create;
