/* eslint-disable react/display-name */
import React, { forwardRef, useImperativeHandle } from 'react';
import styles from './index.module.less';
import { Box, Modal } from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { LoadingButton } from '@mui/lab';
import { observer } from 'mobx-react-lite';
import appStore from '@/store/app';
import Message from "@/components/Snackbar/message";
import copySvg from '@/assets/copy.svg';

const addseticpModalStyles = {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    backgroundColor: '#272938',
    borderRadius: '12px',
    width: '360px',
    padding: '20px',
    outline: 'none',
};
//  BuyTokenModal  props
interface BuyTokenModalProps {
    onopen: boolean;
    ref: React.RefObject<{
        openModal: () => void;
    }>;
}

//  ref
interface BuyTokenModalRef {
    openModal: () => void;
}

const SendWell: React.FC<BuyTokenModalProps> = observer(
    forwardRef<BuyTokenModalRef, BuyTokenModalProps>(({ onopen }, ref) => {
        const [openModalVal, setopenModal] = React.useState(false);

        useImperativeHandle(ref, () => ({
            openModal: () => {
                setopenModal(true);
            },
        }));

        const handleadsenticp = () => {
            setopenModal(false);
        };

		const CanisterCopyBtnFn = (copyText: string) => {
			navigator.clipboard.writeText(copyText).then(
				() => {
					Message.success("Success!");
				},
				() => {
					Message.error("clipboard write failed");
				},
			);
		};

        return (
            <div>
                <Modal
                    className={styles.reciveWellModal}
                    open={openModalVal}
                    onClose={handleadsenticp}
                    style={{ borderColor: '#262939' }}
                >
                    <Box
                        sx={{
                            ...addseticpModalStyles,
                        }}
                    >
                        <div className={styles.reciveWell}>
                            <div onClick={handleadsenticp}>
                                <CloseIcon
                                    sx={{
                                        color: '#fff',
                                        position: 'absolute',
                                        right: '15px',
                                        top: '15px',
                                        cursor: 'pointer',
                                    }}
                                ></CloseIcon>
                            </div>
                            <div className={styles.header} style={{ textAlign: 'center' }}>
                                Reveice WELL
                            </div>

                            <div style={{ width: '100%', marginTop: '40px' }}>
                                <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                                    <div style={{ fontSize: '14px', color: '#fff' }}>Your WELL Principal ID</div>
                                    <div onClick={() => CanisterCopyBtnFn(appStore.userId)}>
                                        <img src={copySvg} style={{ cursor: 'pointer', width: '16px', height: '16px' }} />
                                    </div>
                                </div>

                                <div
                                    style={{
                                        marginTop: '12px',
                                        borderRadius: '8px',
                                        lineHeight: '20px',
                                        textAlign: 'left',
                                        background: '#1B1D28',
                                        padding: '12px',
                                        color: '#fff',
                                        fontSize: '14px',
                                    }}
                                >
                                    {appStore.getUserId()}
                                </div>
                            </div>
                            <LoadingButton
                                id="openbuyLoding"
                                loading={false}
                                className={styles.openbuyAmountLoding}
                                sx={{
                                    color: '#fff',
                                    backgroundImage: "linear-gradient(270deg, #A25FFF 0%, #6931FF 100%)"
                                }}
                                onClick={handleadsenticp}
                            >
                                <div>Close</div>
                                
                            </LoadingButton>
                        </div>
                    </Box>
                </Modal>
            </div>
        );
    }),
);

export default SendWell;
