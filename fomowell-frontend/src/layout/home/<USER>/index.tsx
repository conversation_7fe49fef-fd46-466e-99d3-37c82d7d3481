/* eslint-disable react/display-name */
import React, {
	ChangeEventHand<PERSON>,
	forwardRef,
	useEffect,
	useImperativeHandle,
	useRef,
} from "react";
import styles from "./index.module.less";
import { Principal } from "@dfinity/principal";
import {
	Box,
	InputAdornment,
	InputBase,
	InputLabel,
	Modal,
} from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import {
	divideAndConvertToNumber,
	formatAmountByUnit,
	isValidAccountId,
	isValidPrincipal,
	multiplyAndConvertToBigInt,
} from "@/utils/common";
import Big from "big.js";
import { LoadingButton } from "@mui/lab";
import { observer } from "mobx-react-lite";
import { icrc1_decimals, icrc1balance } from "@/api/icrc2_ledger";
import appStore, { setWellAccount } from "@/store/app";
import { AccountIdentifier } from "@dfinity/ledger-icp";
import { TransferArg } from "@/canisters/well_ledger/well_ledger.did";
import {
	well_icrc1_balance_of, // well_icrc1_balance_of,
	well_icrc1_decimals, // well_icrc1_decimals,
	well_icrc1_transfer, // well_icrc1_transfer,
	well_icrc1_fee, // well_transfer_fee,
	canisterId as wellCanisterId,
} from "@/api/well_ledger";
import Message from "@/components/Snackbar/message";
import SnackbarProgress, {
	SnackbarModalHandles,
} from "@/components/SnackbarProgress/SnackbarProgress";
import walletSvg from "@/assets/wallet.svg";

const addseticpModalStyles = {
	position: "absolute",
	top: "50%",
	left: "50%",
	transform: "translate(-50%, -50%)",
	backgroundColor: "#272938",
	borderRadius: "8px",
	width: "390px",
	padding: "20px",
	outline: "none",
};
//  BuyTokenModal  props
interface BuyTokenModalProps {
	onopen: boolean;
	ref: React.RefObject<{
		openModal: () => void;
	}>;
}

//  ref
interface BuyTokenModalRef {
	openModal: () => void;
}

const SendWell: React.FC<BuyTokenModalProps> = observer(
	forwardRef<BuyTokenModalRef, BuyTokenModalProps>(({ onopen }, ref) => {
		const [openModalVal, setopenModal] = React.useState(false);
		const [amount, setamount] = React.useState<string | number>("");
		const [openbuyAmountLoding, setopenbuyAmountLoding] = React.useState(false);
		const [isshow, setisshow] = React.useState(true);
		const [btnfomoText, setbtnfomoText] = React.useState("Enter WELL amount");
		const [toamount, settoamount] = React.useState<string>();
		const [curWellBalance, setCurWellBalance] = React.useState<number | string>(
			"",
		);
		const [feeVal, setfeeVal] = React.useState<any>(BigInt(10000));
		const [decimal, setdecimal] = React.useState(8);
		const [openRightBox, setopenRightBox] = React.useState(false);
		useImperativeHandle(ref, () => ({
			openModal: () => {
				//
				if (!curWellBalance) {
					balanceReq();
				}
				//
				init();
				setopenModal(true);
			},
		}));
		const init = async () => {
			const fee = await well_icrc1_fee();
			const decimal = (await well_icrc1_decimals());
	
			setdecimal(decimal);
			setfeeVal(fee);
		};
		const handleadsenticp = () => {
			setopenModal(false);
			setamount(0);
		};
		const btnseticp = async () => {
			if (!toamount) {
				return;
			}
			let to_address: Principal;
			if (isValidPrincipal(toamount.toString())) {
				to_address = Principal.fromText(toamount.toString());
			} else {
				throw new Error("Invalid ICP Address!");
			}
			setopenbuyAmountLoding(true);
			const transferAmount = multiplyAndConvertToBigInt(amount, decimal);
			const params: TransferArg = {
				to: {
					owner: to_address,
					subaccount: [],
				},
				fee: [feeVal],
				memo: [],
				from_subaccount: [],
				created_at_time: [],
				amount: BigInt(transferAmount),
			};
			setopenRightBox(true);

			try {
				const result = await well_icrc1_transfer(wellCanisterId, params);
				if ("Ok" in result) {
					Message.success("Transfer successful!");
					setopenbuyAmountLoding(false);
					setopenModal(false);
					setopenRightBox(false);

					const balance = await well_icrc1_balance_of({
						owner: Principal.fromText(appStore.getUserId()),
						subaccount: [],
					});
					setWellAccount(
						new Big(Number(balance))
							.div(new Big(10).pow(Number(decimal)))
							.toString(),
					);
				} else {
					const error = result.Err;
					console.log(error);
					Message.error("Transfer failed");
					setopenbuyAmountLoding(false);
					setopenRightBox(false);
					//   if ('TxTooOld' in error) {
					//     Message.error(
					//       `Transfer failed: Transaction too old. Allowed window nanos: ${error.TxTooOld.allowed_window_nanos}`,
					//     );
					//   } else if ('BadFee' in error) {
					//     Message.error(`Transfer failed: Bad fee. Expected fee: ${error.BadFee.expected_fee}`);
					//   } else if ('InsufficientFunds' in error) {
					//     Message.error(`Transfer failed: Insufficient funds. Balance: ${error.InsufficientFunds.balance}`);
					//   } else if ('TxDuplicate' in error) {
					//     Message.error(`Transfer failed: Duplicate transaction. Duplicate of: ${error.TxDuplicate.duplicate_of}`);
					//   } else if ('TxCreatedInFuture' in error) {
					//     Message.error('Transfer failed: Transaction created in the future.');
					//   } else {
					//     Message.error('Transfer failed: Unknown error.');
					//   }
				}
			} catch (err) {
				console.log(err);
				Message.error("Transfer failed");
				setopenbuyAmountLoding(false);
				setopenRightBox(false);
			}
		};
		const balanceReq = async () => {

			const balance = await well_icrc1_balance_of({
				owner: Principal.fromText(appStore.getUserId()),
				subaccount: [],
			});

			setCurWellBalance(
				new Big(Number(balance))
					.div(new Big(10).pow(Number(decimal)))
					.toString(),
			);
		};

		const btnHalfWELL = () => {
			setamount(
				appStore.wellAccount
					? new Big(appStore.wellAccount)
							.minus(
								divideAndConvertToNumber(feeVal, decimal, 18),
							)
							.div(new Big(2))
							.toString()
					: new Big(curWellBalance)
							.minus(
								divideAndConvertToNumber(feeVal, decimal, 18),
							)
							.div(new Big(2))
							.toString(),
			);
		};

		const btnMaxWELL = () => {
			setamount(
				appStore.wellAccount
					? new Big(appStore.wellAccount)
							.minus(
								divideAndConvertToNumber(feeVal, decimal, 18),
							)
							.toString()
					: new Big(curWellBalance)
							.minus(
								divideAndConvertToNumber(feeVal, decimal, 18),
							)
							.toString(),
			);
		};
		const inputamountchange = (
			e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>,
		) => {
			if (
				typeof Number(e.target.value) == "number" &&
				!isNaN(Number(e.target.value))
			) {
				setamount(e.target.value);
			} else {
				// props.onRouteChange({ type: 'error', content: 'The entered value is incorrect' });
				Message.error("The entered value is incorrect");
			}
			console.log(e);
		};
		const snackbarRef = useRef<SnackbarModalHandles>(null);
		const handleButtonClick = (RightBox: boolean) => {
			if (snackbarRef.current) {
				snackbarRef.current.openSnackbar("WELL transfer in progress", RightBox);
				snackbarRef.current.setViewProgress(true);
			}
		};
		const handleViewProgress = () => {
			console.log("");
		};
		useEffect(() => {
			handleButtonClick(openRightBox);
		}, [openRightBox]);
		useEffect(() => {
			if (amount && amount != 0 && toamount && toamount != "") {
				setisshow(false);

				if (new Big(Number(amount)).gt(new Big(Number(curWellBalance)))) {
					setisshow(true);
					setbtnfomoText("Insufficient balance");
				}
				if (!isValidPrincipal(toamount.toString())) {
					setisshow(true);
					setbtnfomoText("Invalid WELL Address");
				}
			} else {
				setisshow(true);
				if (!amount && amount == 0) {
					setbtnfomoText("Enter WELL amount");
				} else {
					setbtnfomoText("Enter the principal ID");
				}
			}
		}, [amount, toamount]);
		return (
			<div>
				<Modal
					className={styles.sendWellModal}
					open={openModalVal}
					onClose={handleadsenticp}
					style={{ borderColor: "#262939" }}
				>
					<Box
						sx={{
							...addseticpModalStyles,
						}}
					>
						<div className={styles.sendWell}>
							<div onClick={handleadsenticp}>
								<CloseIcon
									sx={{
										color: "#fff",
										position: "absolute",
										right: "15px",
										top: "15px",
										cursor: "pointer",
									}}
								></CloseIcon>
							</div>
							<div className={styles.header} style={{ textAlign: "center" }}>SEND WELL</div>
							<div className={styles.balanceInfo} style={{ marginTop: '30px' }}>
								<div style={{ color: '#fff', fontSize: '14px' }}>
									Amount
								</div>
								<div style={{ textAlign: 'right', width: '100%' }}>
									{/* Balance: */}
									<img src={walletSvg} style={{ width: '14px' }} />
									<span style={{ marginLeft: '7px', marginRight: '7px' }}>
										{formatAmountByUnit(
											appStore.wellAccount
												? appStore.wellAccount
												: curWellBalance?.toString(),
										)}
									</span>
									<span className={styles.MaxICPorBalance} onClick={btnHalfWELL}>
										50%
									</span>
									<span className={styles.MaxICPorBalance} onClick={btnMaxWELL}>
										Max
									</span>
								</div>
							</div>
							
							<div className={styles.amountInput}>
								{/* <InputLabel className={styles.icpamount}>Amount</InputLabel> */}
								<InputBase
									className={styles.amount}
									placeholder="Enter WELL amount"
									value={amount}
									sx={{
										".css-3b6ca1-MuiInputBase-input": {
											border: "1px soild red",
										},
									}}
									// onChange={(e) => setamount(new Big(e.target.value).times(Math.pow(10, cyclesdecimals!)).toFixed(0, 0))}
									onChange={(e) => inputamountchange(e)}
									// required
								></InputBase>
							</div>
							<div style={{ color: '#fff', fontSize: '14px', marginTop: '12px' }}>
								To
							</div>
							<div className={styles.amountInput}>
								{/* <InputLabel className={styles.icpamount}>To</InputLabel> */}
								<InputBase
									className={styles.amount}
									placeholder="Enter the principal ID"
									sx={{
										".css-3b6ca1-MuiInputBase-input": {
											border: "1px soild red",
										},
									}}
									// onChange={(e) => setamount(new Big(e.target.value).times(Math.pow(10, cyclesdecimals!)).toFixed(0, 0))}
									onChange={(e) => settoamount(e.target.value)}
									// required
								></InputBase>
							</div>
							<div style={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', marginTop: '12px', color: '#939CC1', fontSize: '14px' }}>
								Transfer Fee:
								<span className={styles.MaxICPorBalance}>
									<span style={{ color: '#fff' }}>
										{divideAndConvertToNumber(
											feeVal,
											decimal,
											18,
										)}
									</span>
									{" "}
									WELL
								</span>
							</div>
							<LoadingButton
								id="openbuyLoding"
								loading={openbuyAmountLoding}
								className={styles.openbuyAmountLoding}
								sx={{
									color: isshow ? "#eef7ff" : "#fff",
									backgroundImage: isshow
										? "linear-gradient(235deg, #4c516c 0%, #4e5082 100%)"
										: "linear-gradient(270deg, #A25FFF 0%, #6931FF 100%)",
									pointerEvents: isshow ? "none" : "",
								}}
								onClick={btnseticp}
							>
								<div style={{ display: isshow ? "none" : "" }}>Confirm</div>
								<div
									style={{
										fontSize: isshow ? 14 : 14,
										fontWeight: isshow ? "normal" : "normal",
										display: isshow ? "" : "none",
									}}
								>
									{btnfomoText}
								</div>
							</LoadingButton>
						</div>
					</Box>
				</Modal>
				<SnackbarProgress
					ref={snackbarRef}
					onViewProgress={handleViewProgress}
				></SnackbarProgress>
			</div>
		);
	}),
);

export default SendWell;
