.BuyTokenModal {
  position: absolute;
  z-index: 999;
}

.Content {
  width: 400px;
  height: 240px;
  color: #fff;
  padding: 10px;

  .header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .main {
    display: flex;
    flex-direction: column;
    margin-top: 8px;
    padding: 0 35px;

    .balances {
      display: flex;
      justify-content: flex-end;
    }

    .inputInfo {
      margin-top: 10px;
      width: 94%;
      height: 28px;
      border: 1px solid rgba(87, 61, 148, 1);
      border-radius: 4px;
      padding: 10px;
    }

    .TypeTag {
      display: flex;
      justify-content: space-evenly;
      opacity: 0.65;
      font-family: '';
      font-size: 12px;
      color: #FFFFFF;
      letter-spacing: 0;
      font-weight: 600;
      margin-top: 15px;

      .TypeTagItem {
        padding: 5px;
        background: #34384B;
        border-radius: 4px;
        cursor: pointer;
      }

      .TypeTagItem:hover {
        background-color: #4a5065;
      }
    }

    .openbuyLoding {
      height: 40px;
      width: 100%;
      // background-color: #6931FF;
      // background-image: linear-gradient(270deg, #ccc 0%, #ccc 100%);
      border-radius: 4px;
      text-align: center;
      cursor: pointer;
      margin-top: 20px;
      text-transform: none;
      display: flex;
      flex-direction: column;
      font-size: 14px;
      line-height: 19px;
    }
  }
}