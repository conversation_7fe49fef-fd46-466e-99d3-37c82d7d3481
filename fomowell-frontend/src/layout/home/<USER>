import { getShowNewVersionModal, setShowNewVersionModal } from '@/store/app';
import { Box, Button, Fade, Modal } from '@mui/material';
import { observer } from 'mobx-react-lite';
import CloseIcon from '@mui/icons-material/Close';
import { useEffect, useState } from 'react';

interface ChildComponentProps {
    //message under components/Snackbar is no longer used
    onMessageModal: (messageInfo: { type: 'error' | 'info' | 'success' | 'warning'; content: string }) => void;
    openSelectWell: boolean;
    editOpenSelectWell: (Param: boolean) => void;
}

const ModolStyle = {
    position: 'absolute',
    top: '20%',
    left: '50%',
    transform: 'translate(-50%, -50%)',
    // width: 370,
    backgroundColor: 'var(--carmodalbgc)',
    borderRadius: '8px',
    borderColor: '#262939',

    touchAction: 'none',
    outline: 'none !important',
    minWidth: '390px',
    padding: '20px !important',

    // pt: 2,
    // px: 4,
    // pb: 3,
};

const CancelButton = {
    backgroundImage: 'linear-gradient(270deg, #d1d6dc 0%, #d1d6dc  100%)',
    width: '155px',
    height: '35px',
    fontSize: '15px',
    lineHeight: '16px',
    cursor: 'pointer',
    color: '#000',
};

const GoNewVersionButton = {
    backgroundImage: 'linear-gradient(270deg, #A25FFF 0%, #6931FF 100%)',
    width: '155px',
    height: '35px',
    fontSize: '15px',
    lineHeight: '16px',
    cursor: 'pointer',
    color: '#fff',
};

const NewVersionModal: React.FC<ChildComponentProps> = observer(() => {
    const isShow = getShowNewVersionModal();
    const [show, setShow] = useState(isShow);

    useEffect(() => {
        setShow(isShow);
    }, [isShow]);

    const hidden = () => {
        setShow(false);
        setShowNewVersionModal(false);
    };

    return (
        <Modal
            // disableAutoFocus
            open={show}
            sx={{ '.css-pjyw4r': { paddingLeft: '18px', paddingRight: '0px' } }}
            onClose={hidden}
            style={{ borderColor: '#262939', zIndex: '60' }}
        >
            <Fade in={show}>
                <Box sx={{ ...ModolStyle }}>
                    <div style={{ position: 'absolute', right: '20px', top: '5px' }}>
                        <CloseIcon
                            sx={{
                                color: 'rgba(255,255,255,0.45)',
                                height: '45px',
                                cursor: 'pointer',
                            }}
                            onClick={() => hidden()}
                        ></CloseIcon>
                    </div>

                    <div className="my-4 p-10 text-base text-[#fff] text-center leading-8">
                        Do you want to access the new version of Fomowell?
                    </div>

                    <div className="flex items-center justify-center gap-x-5">
                        <Button variant="text" sx={{ ...CancelButton }} onClick={() => hidden()}>
                            cancel
                        </Button>
                        <Button
                            variant="text"
                            sx={{ ...GoNewVersionButton }}
                            onClick={() => {
                                window.open('https://app.fomowell.com', '_blank');
                            }}
                        >
                            Experience now
                        </Button>
                    </div>
                </Box>
            </Fade>
        </Modal>
    );
});

export default NewVersionModal;
