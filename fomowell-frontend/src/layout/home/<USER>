import { ConnectButton } from '@rainbow-me/rainbowkit';
import { useChainId, useSwitch<PERSON>hain } from 'wagmi';
import { useEffect, useState } from 'react';
import styles from './index.module.less';
import { Button, ButtonGroup } from '@mui/material';
import LogoutIcon from '@mui/icons-material/Logout';
import { truncateString } from '@/utils/principal';
import Message from '@/components/Snackbar/message';
import { BASE_CHAIN_ID } from '@/api/initWallet';

const main_net = BASE_CHAIN_ID;

const ConnectButtonCustom = () => {
    const chain = useChainId();
    const { chains, switchChain } = useSwitchChain();

    useEffect(() => {
        if (chain !== main_net) {
            const targetChain = chains.find((c) => c.id === main_net);

            if (targetChain) {
                switchChain({ chainId: main_net });
            } else {
                const targetChain = chains.find((c) => c.id === main_net);
                // If the network is not available, prompt the user to add it
                if (window.ethereum && targetChain) {
                    window.ethereum.request(targetChain).catch((error: any) => {
                        console.error('Failed to add network:', error);
                    });
                } else {
                    console.error('chain not allowed');
                }
            }
        }
    }, [chain, chains, switchChain]);

    const onCopyAddress = (copyText: string) => {
        navigator.clipboard.writeText(copyText).then(
            () => {
                Message.success('Copy Success!');
            },
            () => {
                Message.error('clipboard write failed');
            },
        );
    };

    return (
        <div>
            <ConnectButton.Custom>
                {({ account, chain, openConnectModal, mounted, openAccountModal }) => {
                    const connected = mounted && account && chain;
                    return (
                        <div>
                            {(() => {
                                if (!connected) {
                                    return (
                                        <Button
                                            className={styles.ConnectWallet}
                                            sx={{ color: '#fff' }}
                                            onClick={openConnectModal}
                                        >
                                            <div className={styles.top}>{'Connect Wallet'}</div>
                                        </Button>
                                    );
                                }

                                return (
                                    <ButtonGroup variant="contained" aria-label="" className="ml-3">
                                        <Button
                                            sx={{
                                                color: '#fff',
                                                background: '#655EA7',
                                                fontSize: '14px',
                                                '&:hover': {
                                                    background: '#655EA7',
                                                },
                                            }}
                                            onClick={() => {
                                                onCopyAddress(account.address);
                                            }}
                                        >
                                            {truncateString(account.address)}
                                        </Button>
                                        <Button
                                            onClick={openAccountModal}
                                            sx={{
                                                color: '#333',
                                                background: '#d1d6dc',
                                                fontSize: '13px',
                                                padding: 0,
                                                width: '40px',
                                                '&:hover': {
                                                    background: '#d1d6dc',
                                                },
                                            }}
                                        >
                                            <LogoutIcon />
                                        </Button>
                                    </ButtonGroup>
                                );
                            })()}
                        </div>
                    );
                }}
            </ConnectButton.Custom>
        </div>
    );
};

export default ConnectButtonCustom;
