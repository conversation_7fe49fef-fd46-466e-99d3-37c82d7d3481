import { AppThemeProvider } from "./themes/AppThemeProvider";
import ReactDOM from "react-dom/client";
// import 'babel-polyfill';
import { Provider } from "react-redux";
import store from "./app/store";
import React from "react";
import App from "./App";
import "./main.css";
import {
	QueryClient,
	QueryClientProvider,
	useQuery,
} from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import SnsNoticeModal from "./components/New/sns-notice-modal";
const queryClient = new QueryClient();
ReactDOM.createRoot(document.getElementById("root") as HTMLElement).render(
	<QueryClientProvider client={queryClient}>
		<App />
		<ReactQueryDevtools />
	</QueryClientProvider>,
	// <React.StrictMode>
	//   <Provider store={store}>
	//     <AppThemeProvider>
	//       <App />
	//     </AppThemeProvider>
	//   </Provider>
	// </React.StrictMode>,
);
