import { create } from "zustand";
import { persist } from "zustand/middleware";

interface ModalState {
	snsNoticeModalShowed: boolean;
	setSnsNoticeModalShowed: (snsNoticeModalShowed: boolean) => void;
}

const useModalStore = create<ModalState>()(
	persist(
		(set) => ({
			snsNoticeModalShowed: false,
			setSnsNoticeModalShowed: (snsNoticeModalShowed: boolean) =>
				set({ snsNoticeModalShowed }),
		}),
		{
			name: "modal-storage",
		},
	),
);

export default useModalStore;
