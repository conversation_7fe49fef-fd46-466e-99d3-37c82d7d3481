import { API_URL, requestApi } from '@/utils/request/request_base';
import { BuildTransaction, BuyPayload, SellPayload, TokenItem, TransactionType } from './types';
import axios from 'axios';
import { TransactionReceipt } from 'ethers';
import { routerAddress } from './initWallet';
import { ethers, TransactionRequest } from 'ethers';

// create token
export async function createToken(transaction: TransactionType) {
    if (!transaction) throw new Error('createToken no transaction');

    const ethereum = window.ethereum;
    const provider = new ethers.BrowserProvider(ethereum);
    const signer = await provider.getSigner();
    try {
        const tx: TransactionRequest = {
            from: transaction.from,
            to: transaction.to,
            value: BigInt(transaction.value),
            gasLimit: BigInt(transaction.gas),
            nonce: transaction.nonce,
            chainId: transaction.chainId,
            data: transaction.data,
        };

        const txResponse = await signer.sendTransaction(tx);
        const txReceipt = await txResponse.wait();

        return txReceipt;
    } catch (error) {
        console.debug('🚀 ~ createToken ~ error:', error);
        throw new Error('createToken fail!');
    }
}

export async function sendTransaction(transaction: TransactionType) {
    if (!transaction) throw new Error('sendTransaction no transaction');

    const ethereum = window.ethereum;
    const provider = new ethers.BrowserProvider(ethereum);
    const signer = await provider.getSigner();

    try {
        const tx: TransactionRequest = {
            from: transaction.from,
            to: transaction.to,
            gasLimit: BigInt(transaction.gas),
            nonce: transaction.nonce,
            chainId: transaction.chainId,
            data: transaction.data,
        };

        if (transaction.value) {
            tx.value = BigInt(transaction.value);
        }

        const txResponse = await signer.sendTransaction(tx);
        return await txResponse.wait();
    } catch (error) {
        console.debug('🚀 ~ sendTransaction ~ error:', error);
        throw new Error('sendTransaction fail!');
    }
}

// get top coin
export const getTopCoin = async () => {
    return await requestApi<any>(`/kingOfTheHill`, 'get', {});
};

export const buildTransaction = async (payload: BuildTransaction) => {
    const response = await requestApi<{ transaction: TransactionType }>(
        '/build_mint_token_transaction',
        'post',
        payload,
    );
    return response.transaction;
};

// get coins
export const getCoinList = async ({
    sort = 'marketCap',
    search = '',
}: {
    page?: number;
    limit?: number;
    account?: string;
    sort: string;
    search?: string;
}): Promise<{ token_list: TokenItem[] }> => {
    let other = '';
    if (search) {
        other += `&search=${search}`;
    }

    if (sort) {
        other += `&sort=${sort}`;
    }
    console.debug('🚀 ~ other:', other);

    // ${other}
    return await requestApi(`/token_list`, 'GET', {});
};

export const uploadFile = async (file: File) => {
    try {
        const formData = new FormData();
        formData.append('file', file);

        const response = await axios.post(`${API_URL}/upload-to-pinata`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data',
            },
        });

        const result = await response.data;

        return result['ipfs_uri'];
    } catch (error) {
        console.debug('🚀 ~ uploadFile ~ error:', error);
        throw new Error('request fail!');
    }
};

// token candlesticks
export const getSellCalculateTokenToEth = async (amount: string, token: string) => {
    return await requestApi<any>(`/token/calculate/sell?amount=${amount}&bondingCurve=${token}`, 'GET', {});
};

// token candlesticks
export const getBuyCalculateEthToToken = async (amount: string, token: string) => {
    return await requestApi<any>(`/token/calculate/buy?amount=${amount}&bondingCurve=${token}`, 'GET', {});
};

// token item info
export const getTokenItemInfo = async (token: string) => {
    return await requestApi<{ token: TokenItem }>(`/token/${token}`, 'GET', {});
};

// user token count
export const getUserTokenBalance = async (token: string, address: string) => {
    return await requestApi<any>(`/${token}/holder/${address}`, 'GET', {});
};

// holders
export const getHoldersList = async (tokenAddress: string) => {
    const response = await requestApi<any>(`/${tokenAddress}/holders`, 'get', {});
    return response;
};

// token candlesticks
export const getTokenCandlesticks = async (token: string) => {
    return await requestApi<any>(`/${token}/candlesticks/`, 'GET', {});
};

// trades
export const getTrades = async (tokenAddress: string) => {
    const response = await requestApi<any>(`/${tokenAddress}/trades`, 'get', {});
    return response;
};

export const allowance = async (payload: { from_address: string; bc_address: string }) => {
    return await requestApi<any>('/check_allowance', 'post', {
        owner: payload.from_address,
        bc_address: payload.bc_address,
        spender: routerAddress,
    });
};

export const approve = async (payload: SellPayload): Promise<void | TransactionReceipt | null | undefined> => {
    return requestApi<any>('/build_approve_transaction', 'post', {
        from_address: payload.from_address,
        bc_address: payload.bc_address,
        spender: routerAddress,
        amount: payload.sell_bc_amount,
    })
        .then((res) => {
            return sendTransaction(res.transaction).then(async () => {
                return await sell(payload);
            });
        })
        .catch((error) => {
            console.debug('🚀 ~ approve ~ error:', error);
        });
};

export const allowanceAndApprove = async (payload: SellPayload) => {
    const result = await allowance(payload);

    if (result.allowance < payload.sell_bc_amount * 1e18) {
        const res = await approve(payload);
        return res;
    }

    const sellResult = await sell(payload);
    return sellResult;
};

export const sell = async (payload: SellPayload): Promise<void | TransactionReceipt | null | undefined> => {
    return requestApi('/build_sell_token_transaction', 'post', payload)
        .then(async (res: any) => {
            return await sendTransaction(res.transaction);
        })
        .catch((error) => {
            console.debug('🚀 ~ approve ~ error:', error);
        });
};

// buy
export const buildBuyTransaction = async (payload: BuyPayload) => {
    const response = await requestApi<any>('/build_buy_token_transaction', 'post', payload);
    return response.transaction;
};
