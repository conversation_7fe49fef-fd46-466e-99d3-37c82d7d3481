import { createWalletActor, createActor } from '@/utils/agent/create-actor.js';
import type { MetadataValue, _SERVICE, Account, TransferArg, Result } from '@/canisters/well_ledger/well_ledger.did';
import { idlFactory } from '@/canisters/well_ledger';
export let canisterId: string;
if (process.env.CANISTER_ID_WELL_LEDGER) {
  canisterId = process.env.CANISTER_ID_WELL_LEDGER;
  // console.log(canisterId);
} else if (process.env.WELL_LEDGER_CANISTER_ID) {
  canisterId = process.env.WELL_LEDGER_CANISTER_ID;
} else {
  console.error('No CANISTER_ID found in environment variables.');
}

export async function well_icrc1_metadata(canisterIdParams?: string): Promise<Array<[string, MetadataValue]>> {
  let Actor;
  if (canisterIdParams) {
    Actor = createActor<_SERVICE>(canisterIdParams, idlFactory);
    return Actor.icrc1_metadata();
  }
  Actor = createActor<_SERVICE>(canisterId, idlFactory);
  return Actor.icrc1_metadata();
}

export async function well_icrc1_symbol(canisterIdParams?: string): Promise<string> {
  let Actor;
  if (canisterIdParams) {
    Actor = createActor<_SERVICE>(canisterIdParams, idlFactory);
    return Actor.icrc1_symbol();
  }
  Actor = createActor<_SERVICE>(canisterId, idlFactory);
  return Actor.icrc1_symbol();
}

//decimals
export async function well_icrc1_decimals(): Promise<number> {
  const Actor = createActor<_SERVICE>(canisterId, idlFactory);
  return Actor.icrc1_decimals();
}

//icrc1balance , canisterIdParams?: string
export async function well_icrc1_balance_of(Params: Account): Promise<bigint> {
  const Actor = createActor<_SERVICE>(canisterId, idlFactory);
  return Actor.icrc1_balance_of(Params);
}

export async function well_icrc1_transfer(canisterId: string, params: TransferArg): Promise<Result> {
  const Actor: _SERVICE = await createWalletActor(canisterId, idlFactory);
  return Actor.icrc1_transfer(params);
}

//icrc1_fee
export async function well_icrc1_fee(): Promise<bigint> {
  const Actor: _SERVICE = await createActor(canisterId, idlFactory);
  return Actor.icrc1_fee();
}

// icrc1_decimals
export async function well_icrc1_decimals_token(canisterId: string): Promise<number> {
  const Actor: _SERVICE = await createActor(canisterId, idlFactory);
  return Actor.icrc1_decimals();
}
