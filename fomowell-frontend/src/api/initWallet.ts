import { ethers, TransactionRequest } from 'ethers';
import { BASE_UR_CONTRACT_ADDRESS, MAX_COUNT_TO_SHOOT, MAX_TOKEN_TO_SHOOT } from '@/utils/env';
import { TransactionType } from './types';
import { base, baseSepolia } from 'wagmi/chains';

// !base mainnet
const BASE_SEPOLIA_CHAIN_ID = base.id;
const BASE_CHAIN = base;
const BASE_CHAIN_ID = base.id;
const BASE_CHAIN_EXPLORE = base.blockExplorers.default.url;
// // !testnet
// const BASE_SEPOLIA_CHAIN_ID = baseSepolia.id;
// const BASE_CHAIN = baseSepolia;
// const BASE_CHAIN_ID = baseSepolia.id;
// const BASE_CHAIN_EXPLORE = baseSepolia.blockExplorers.default.url;

// 定时刷新最大次数
const MAX_REFETCH_COUNT = 100;

// ! router
const UR_CONTRACT_ADDRESS = BASE_UR_CONTRACT_ADDRESS;

// ! max count
const totalSupplyCount = MAX_COUNT_TO_SHOOT;
const maxTokenSupply = MAX_TOKEN_TO_SHOOT;

// symbol
const getSymbol = () => 'ETH';

// ! pre set amounts
const getPreSetAmounts = () => {
    const symbol = getSymbol();
    return [
        { label: `0.05 ${symbol}`, value: 0.05 },
        { label: `0.1 ${symbol}`, value: 0.1 },
        { label: `0.3 ${symbol}`, value: 0.3 },
        { label: `0.5 ${symbol}`, value: 0.5 },
    ];
};

// create token
async function createToken(transaction: TransactionType) {
    if (!transaction) throw new Error('createToken no transaction');

    const ethereum = window.ethereum;
    const provider = new ethers.BrowserProvider(ethereum);
    const signer = await provider.getSigner();
    try {
        const tx: TransactionRequest = {
            from: transaction.from,
            to: transaction.to,
            value: BigInt(transaction.value),
            gasLimit: BigInt(transaction.gas),
            nonce: transaction.nonce,
            chainId: transaction.chainId,
            data: transaction.data,
        };

        const txResponse = await signer.sendTransaction(tx);
        const txReceipt = await txResponse.wait();

        return txReceipt;
    } catch (error) {
        console.debug('🚀 ~ createToken ~ error:', error);
        throw new Error('createToken fail!');
    }
}

async function sendTransaction(transaction: TransactionType) {
    if (!transaction) throw new Error('sendTransaction no transaction');

    const ethereum = window.ethereum;
    const provider = new ethers.BrowserProvider(ethereum);
    const signer = await provider.getSigner();

    try {
        const tx: TransactionRequest = {
            from: transaction.from,
            to: transaction.to,
            gasLimit: BigInt(transaction.gas),
            nonce: transaction.nonce,
            chainId: transaction.chainId,
            data: transaction.data,
        };

        if (transaction.value) {
            tx.value = BigInt(transaction.value);
        }

        const txResponse = await signer.sendTransaction(tx);
        return await txResponse.wait();
    } catch (error) {
        console.debug('🚀 ~ sendTransaction ~ error:', error);
        throw new Error('sendTransaction fail!');
    }
}

export {
    // base
    getSymbol,
    getPreSetAmounts,
    // router
    // createToken,
    // sendTransaction,
    UR_CONTRACT_ADDRESS as routerAddress,
    // other
    MAX_REFETCH_COUNT,
    totalSupplyCount,
    maxTokenSupply,
    BASE_CHAIN_EXPLORE,
    BASE_CHAIN_ID,
    BASE_CHAIN,
    BASE_SEPOLIA_CHAIN_ID,
};
