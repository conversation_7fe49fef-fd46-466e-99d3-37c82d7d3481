export type BuildTransaction = {
    from_address: `0x${string}` | undefined;
    token_icon: string | undefined;
    token_name: string;
    token_symbol: string;
    token_description: string | undefined;
    mint_fee: string;
    telegram_link?: string | undefined;
    website?: string | undefined;
    twitter_link?: string | undefined;
};

export type TransactionType = {
    from: string;
    to: string;
    value: string | number | bigint | boolean;
    gas: string | number | bigint | boolean;
    nonce: number;
    chainId: string;
    data: string;
};

export type BuyPayload = {
    from_address: string;
    address: string;
    value: number;
};

export type SellPayload = {
    from_address: string;
    address: string;
    bc_address: string;
    sell_bc_amount: number;
};

export type TokenInfo = {
    name: string;
    symbol: string;
    decimals: string;
    totalSupply: string;
    balance: number | string;
};

export type TokenItem = {
    _id: string;
    address: string;
    blockHash: string;
    blockNumber: number;
    erc20TokenAddress: string;
    event: string;
    logIndex: number;
    name: string;
    owner: string;
    symbol: string;
    token: string;
    transactionHash: string;
    transactionIndex: number;
    image?: string;
    uri?: string;
    market_cap_eth?: number;
    description?: string;
    website?: string;
    telegramLink?: string;
    twitterLink?: string;
};

export type TokenAllInfo = TokenInfo & TokenItem;

export type HolderItem = {
    bondingCurve: string;
    balance: number;
    from: string;
    id: string;
};

export type TransactionItem = {
    address: string;
    blockHash: string;
    blockNumber: number;
    from: string;
    event: string;
    logIndex: number;
    tokenAmount: number;
    transactionHash: string;
    transactionIndex: number;
    timestamp: number | string;
    _id: string;
};
