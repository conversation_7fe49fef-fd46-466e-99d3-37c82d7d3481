export const idlFactory = ({ IDL }) => {
  const TransactionStatus = IDL.Variant({
    'Failed' : IDL.Null,
    'Succeeded' : IDL.Null,
    'BaseTrans' : IDL.Null,
    'QuoteTrans' : IDL.Null,
    'Rollback' : IDL.Null,
    'Pending' : IDL.Null,
  });
  const Operation = IDL.Variant({
    'AddLiquidity' : IDL.Null,
    'Approve' : IDL.Null,
    'RemoveTokenController' : IDL.Null,
    'CreateToken' : IDL.Null,
    'Burn' : IDL.Null,
    'Mint' : IDL.Null,
    'Swap' : IDL.Null,
    'CanisterCreated' : IDL.Null,
    'CreatePool' : IDL.Null,
    'CanisterCalled' : IDL.Null,
    'Transfer' : IDL.Null,
    'TransferFrom' : IDL.Null,
    'RemoveLiquidity' : IDL.Null,
  });
  const PoolTxRecord = IDL.Record({
    'to' : IDL.Principal,
    'status' : TransactionStatus,
    'base_reserve' : IDL.Opt(IDL.Float64),
    'pool' : IDL.Opt(IDL.Principal),
    'quote_price' : IDL.Float64,
    'base_price' : IDL.Float64,
    'quote_token' : IDL.Principal,
    'operation' : Operation,
    'timestamp' : IDL.Nat64,
    'base_price_cumulative_last' : IDL.Opt(IDL.Float64),
    'quote_reserve' : IDL.Opt(IDL.Float64),
    'base_token' : IDL.Principal,
    'quote_amount' : IDL.Float64,
    'caller' : IDL.Opt(IDL.Principal),
    'base_amount' : IDL.Float64,
    'tx_hash' : IDL.Nat64,
    'tx_hash_str' : IDL.Text,
    'fail_msg' : IDL.Text,
  });
  const IToken = IDL.Record({ 'cid' : IDL.Principal });
  const Router = IDL.Record({
    'fee_token' : IToken,
    'owner' : IDL.Principal,
    'user_pools' : IDL.Vec(IDL.Tuple(IDL.Principal, IDL.Vec(IDL.Principal))),
    'icpex_wasm' : IDL.Vec(IDL.Nat8),
    'default_mt_fee_rate' : IDL.Nat,
  });
  const AddrConfig = IDL.Record({
    'router_addr' : IDL.Principal,
    'oracle_addr' : IDL.Principal,
    'tx_addr' : IDL.Principal,
    'backend_addr' : IDL.Principal,
    'icpl_addr' : IDL.Principal,
  });
  const RState = IDL.Variant({
    'ONE' : IDL.Null,
    'AboveOne' : IDL.Null,
    'BelowOne' : IDL.Null,
  });
  const PoolStatus = IDL.Variant({
    'CREATE_BASE_INPUT' : IDL.Null,
    'CREATE_QUOTE_INPUT' : IDL.Null,
    'OFFLINE' : IDL.Null,
    'ROLLBACK_UNDONE' : IDL.Null,
    'CREATED' : IDL.Null,
    'ROLLBACK_DONE' : IDL.Null,
    'ONLINE' : IDL.Null,
  });
  const PPool = IDL.Record({
    'i' : IDL.Nat,
    'k' : IDL.Nat,
    'r_state' : RState,
    'base_reserve' : IDL.Nat,
    'owner' : IDL.Principal,
    'block_timestamp_last' : IDL.Nat64,
    'pool_addr' : IDL.Principal,
    'lp_fee_rate' : IDL.Nat,
    'quote_target' : IDL.Nat,
    'base_target' : IDL.Nat,
    'quote_token' : IToken,
    'base_price_cumulative_last' : IDL.Nat,
    'quote_reserve' : IDL.Nat,
    'base_token' : IToken,
    'base_decimals' : IDL.Nat8,
    'pool_status' : PoolStatus,
    'mt_fee_rate' : IDL.Nat,
    'quote_decimals' : IDL.Nat8,
  });
  const DIP20 = IDL.Record({
    'decimals' : IDL.Nat8,
    'flat_fee' : IDL.Bool,
    'fee_to' : IDL.Principal,
    'owner' : IDL.Principal,
    'logo' : IDL.Text,
    'name' : IDL.Text,
    'mint_on' : IDL.Bool,
    'burn_rate' : IDL.Nat,
    'fee_rate' : IDL.Nat,
    'burn_on' : IDL.Bool,
    'allowances' : IDL.Vec(
      IDL.Tuple(IDL.Principal, IDL.Vec(IDL.Tuple(IDL.Principal, IDL.Nat)))
    ),
    'flat_burn_fee' : IDL.Bool,
    'total_supply' : IDL.Nat,
    'symbol' : IDL.Text,
    'balances' : IDL.Vec(IDL.Tuple(IDL.Principal, IDL.Nat)),
  });
  const CPool = IDL.Record({
    'i' : IDL.Nat,
    'k' : IDL.Nat,
    'base_reserve' : IDL.Nat,
    'owner' : IDL.Principal,
    'block_timestamp_last' : IDL.Nat64,
    'pool_addr' : IDL.Principal,
    'lp_fee_rate' : IDL.Nat,
    'shares_ledger' : DIP20,
    'quote_token' : IToken,
    'expired_lock_ledger' : IDL.Vec(
      IDL.Tuple(IDL.Principal, IDL.Nat, IDL.Nat64)
    ),
    'base_price_cumulative_last' : IDL.Nat,
    'quote_reserve' : IDL.Nat,
    'base_token' : IToken,
    'base_decimals' : IDL.Nat8,
    'pool_status' : PoolStatus,
    'mt_fee_rate' : IDL.Nat,
    'quote_decimals' : IDL.Nat8,
  });
  const StablePool = IDL.Record({
    'i' : IDL.Nat,
    'k' : IDL.Nat,
    'r_state' : RState,
    'base_reserve' : IDL.Nat,
    'owner' : IDL.Principal,
    'block_timestamp_last' : IDL.Nat64,
    'pool_addr' : IDL.Principal,
    'lp_fee_rate' : IDL.Nat,
    'quote_target' : IDL.Nat,
    'shares_ledger' : DIP20,
    'base_target' : IDL.Nat,
    'quote_token' : IToken,
    'expired_lock_ledger' : IDL.Vec(
      IDL.Tuple(IDL.Principal, IDL.Nat, IDL.Nat64)
    ),
    'base_price_cumulative_last' : IDL.Nat,
    'quote_reserve' : IDL.Nat,
    'base_token' : IToken,
    'base_decimals' : IDL.Nat8,
    'pool_status' : PoolStatus,
    'mt_fee_rate' : IDL.Nat,
    'quote_decimals' : IDL.Nat8,
  });
  const PoolExecuteType = IDL.Variant({
    'PPOOL' : PPool,
    'UNKNOWN' : IDL.Null,
    'CPOOL' : CPool,
    'SPOOL' : StablePool,
  });
  const Result = IDL.Variant({
    'Ok' : IDL.Tuple(IDL.Principal, IDL.Nat),
    'Err' : IDL.Text,
  });
  const PoolInfoConfig = IDL.Record({
    'i' : IDL.Nat,
    'k' : IDL.Nat,
    'r_state' : IDL.Text,
    'base_reserve' : IDL.Nat,
    'owner' : IDL.Principal,
    'block_timestamp_last' : IDL.Nat64,
    'pool_addr' : IDL.Principal,
    'pool_type' : IDL.Text,
    'lp_fee_rate' : IDL.Nat,
    'quote_target' : IDL.Nat,
    'base_target' : IDL.Nat,
    'quote_token' : IDL.Principal,
    'base_price_cumulative_last' : IDL.Nat,
    'quote_reserve' : IDL.Nat,
    'base_token' : IDL.Principal,
    'base_decimals' : IDL.Nat8,
    'pool_status' : IDL.Text,
    'mt_fee_rate' : IDL.Nat,
    'quote_decimals' : IDL.Nat8,
    'balances' : IDL.Vec(IDL.Tuple(IDL.Principal, IDL.Nat)),
  });
  const PoolInfo = IDL.Record({
    'i' : IDL.Nat,
    'k' : IDL.Nat,
    'base_reserve' : IDL.Nat,
    'owner' : IDL.Principal,
    'block_timestamp_last' : IDL.Nat64,
    'pool_addr' : IDL.Principal,
    'lp_amount' : IDL.Nat,
    'pool_type' : IDL.Text,
    'lp_lock' : IDL.Nat,
    'quote_user' : IDL.Nat,
    'lp_fee_rate' : IDL.Nat,
    'base_token_decimals' : IDL.Nat8,
    'quote_token_decimals' : IDL.Nat8,
    'base_user' : IDL.Nat,
    'quote_token' : IDL.Principal,
    'base_price_cumulative_last' : IDL.Nat,
    'quote_reserve' : IDL.Nat,
    'base_token' : IDL.Principal,
    'pool_status' : PoolStatus,
    'mt_fee_rate' : IDL.Nat,
    'is_single_pool' : IDL.Bool,
    'total_supply' : IDL.Nat,
    'is_my_pool' : IDL.Bool,
  });
  const PoolAmountInfo = IDL.Record({
    'base_token_price' : IDL.Nat,
    'lp_amount' : IDL.Nat,
    'lp_lock' : IDL.Nat,
    'lp_fee_rate' : IDL.Nat,
    'base_token_decimals' : IDL.Nat,
    'quote_token_decimals' : IDL.Nat,
    'quote_token' : IDL.Principal,
    'base_token' : IDL.Principal,
    'quote_token_price' : IDL.Nat,
    'quote_amount' : IDL.Nat,
    'base_amount' : IDL.Nat,
    'pool_address' : IDL.Principal,
    'mt_fee_rate' : IDL.Nat,
    'total_supply' : IDL.Nat,
  });
  const Account = IDL.Record({
    'owner' : IDL.Principal,
    'subaccount' : IDL.Opt(IDL.Vec(IDL.Nat8)),
  });
  const Result_1 = IDL.Variant({ 'Ok' : IDL.Float32, 'Err' : IDL.Text });
  const PoolOrder = IDL.Record({
    'i' : IDL.Nat,
    'k' : IDL.Nat,
    'base_in_amount' : IDL.Nat,
    'quote_in_amount' : IDL.Nat,
    'deadline' : IDL.Nat64,
    'fee_rate' : IDL.Nat,
    'quote_token_addr' : IDL.Principal,
    'caller' : IDL.Principal,
    'base_token_addr' : IDL.Principal,
  });
  const SwapOrder = IDL.Record({
    'base_from_token' : IDL.Principal,
    'base_from_amount' : IDL.Nat,
    'directions' : IDL.Nat64,
    'swap_caller' : IDL.Principal,
    'deadline' : IDL.Nat64,
    'base_min_return_amount' : IDL.Nat,
    'base_to_token' : IDL.Principal,
    'pairs' : IDL.Vec(IDL.Principal),
  });
  const Result_2 = IDL.Variant({ 'Ok' : IDL.Null, 'Err' : IDL.Text });
  const Result_3 = IDL.Variant({
    'Ok' : IDL.Tuple(
      IDL.Nat,
      IDL.Vec(IDL.Principal),
      IDL.Nat8,
      IDL.Nat,
      IDL.Nat,
    ),
    'Err' : IDL.Text,
  });
  const Result_4 = IDL.Variant({ 'Ok' : IDL.Nat64, 'Err' : IDL.Text });
  return IDL.Service({
    'addInnerLiquidity' : IDL.Func(
        [IDL.Principal, IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat64, PoolTxRecord],
        [IDL.Nat, IDL.Nat, IDL.Nat],
        [],
      ),
    'addLiquidity' : IDL.Func(
        [IDL.Principal, IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat64],
        [IDL.Nat, IDL.Nat, IDL.Nat],
        [],
      ),
    'addPrivateLiquidity' : IDL.Func(
        [IDL.Principal, IDL.Nat, IDL.Nat, IDL.Nat64],
        [IDL.Nat, IDL.Nat],
        [],
      ),
    'addStableLiquidity' : IDL.Func(
        [IDL.Principal, IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat64],
        [IDL.Nat, IDL.Nat, IDL.Nat],
        [],
      ),
    'adminData' : IDL.Func(
        [],
        [
          Router,
          AddrConfig,
          IDL.Vec(IDL.Tuple(IDL.Principal, PoolExecuteType)),
        ],
        ['query'],
      ),
    'adminUpdateCanister' : IDL.Func([], [], ['query']),
    'adminUpdateLp' : IDL.Func([IDL.Principal, IDL.Nat, IDL.Principal], [], []),
    'adminUpdatePoolParam' : IDL.Func(
        [IDL.Principal, IDL.Nat, IDL.Nat],
        [],
        [],
      ),
    'adminUpdateTokenCache' : IDL.Func([IDL.Principal], [], []),
    'createCommonPool' : IDL.Func(
        [
          IDL.Principal,
          IDL.Principal,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat64,
        ],
        [Result],
        [],
      ),
    'createPrivatePool' : IDL.Func(
        [
          IDL.Principal,
          IDL.Principal,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat64,
        ],
        [Result],
        [],
      ),
    'createStablePool' : IDL.Func(
        [
          IDL.Principal,
          IDL.Principal,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat64,
        ],
        [Result],
        [],
      ),
    'cycles' : IDL.Func([], [IDL.Nat64], ['query']),
    'getAllPoolInfo' : IDL.Func([], [IDL.Vec(PoolInfoConfig)], ['query']),
    'getLp' : IDL.Func(
        [IDL.Principal, IDL.Principal],
        [IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat],
        ['query'],
      ),
    'getMidPrice' : IDL.Func([IDL.Principal], [IDL.Nat], ['query']),
    'getPoolInfo' : IDL.Func(
        [IDL.Principal, IDL.Principal],
        [PoolInfo],
        ['query'],
      ),
    'getPoolsAmountInfo' : IDL.Func(
        [],
        [IDL.Vec(PoolAmountInfo), IDL.Nat64],
        ['query'],
      ),
    'getPoolsAmountInfoUncache' : IDL.Func(
        [],
        [IDL.Vec(PoolAmountInfo), IDL.Nat64],
        [],
      ),
    'getPoolsInfo' : IDL.Func([IDL.Principal], [IDL.Vec(PoolInfo)], ['query']),
    'getSubaccount' : IDL.Func([], [Account], ['query']),
    'getSubaccountAdmin' : IDL.Func([IDL.Principal], [Account], ['query']),
    'getTimePrice' : IDL.Func([IDL.Principal], [IDL.Nat], ['query']),
    'get_deviation_rate' : IDL.Func(
        [IDL.Nat, IDL.Nat, IDL.Principal, IDL.Nat64],
        [Result_1],
        ['query'],
      ),
    'initAddrConfig' : IDL.Func([IDL.Principal, IDL.Principal], [], []),
    'innerAddPrivateLiquidity' : IDL.Func(
        [IDL.Principal, IDL.Nat, IDL.Nat, IDL.Nat64, PoolTxRecord],
        [IDL.Nat, IDL.Nat],
        [],
      ),
    'innerAddStableLiquidity' : IDL.Func(
        [IDL.Principal, IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat64, PoolTxRecord],
        [IDL.Nat, IDL.Nat, IDL.Nat],
        [],
      ),
    'innerCreateCommonPool' : IDL.Func(
        [PoolOrder, PoolTxRecord],
        [IDL.Principal, IDL.Nat],
        [],
      ),
    'innerCreatePrivatePool' : IDL.Func(
        [PoolOrder, PoolTxRecord],
        [IDL.Principal, IDL.Nat],
        [],
      ),
    'innerCreateStablePool' : IDL.Func(
        [PoolOrder, PoolTxRecord],
        [IDL.Principal, IDL.Nat],
        [],
      ),
    'innerSellShares' : IDL.Func(
        [
          IDL.Nat,
          IDL.Principal,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat,
          IDL.Nat64,
          PoolTxRecord,
        ],
        [IDL.Nat, IDL.Nat],
        [],
      ),
    'innerSwapTokenToToken' : IDL.Func([SwapOrder], [IDL.Nat], []),
    'lockLiquidity' : IDL.Func(
        [IDL.Principal, IDL.Nat, IDL.Nat64],
        [Result_2],
        [],
      ),
    'markIcrc1SubWallet' : IDL.Func([IDL.Principal, IDL.Principal], [], []),
    'platformWithdraw' : IDL.Func(
        [IDL.Nat, IDL.Principal, IDL.Principal],
        [Result_2],
        [],
      ),
    'queryAddShareBase' : IDL.Func(
        [IDL.Nat, IDL.Principal],
        [IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat],
        ['query'],
      ),
    'queryAddShareQuote' : IDL.Func(
        [IDL.Nat, IDL.Principal],
        [IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat],
        ['query'],
      ),
    'querySellBase' : IDL.Func(
        [IDL.Principal, IDL.Nat],
        [IDL.Nat, IDL.Nat, IDL.Nat, RState, IDL.Nat],
        ['query'],
      ),
    'querySellQuote' : IDL.Func(
        [IDL.Principal, IDL.Nat],
        [IDL.Nat, IDL.Nat, IDL.Nat, RState, IDL.Nat],
        ['query'],
      ),
    'querySellShares' : IDL.Func(
        [IDL.Nat, IDL.Principal, IDL.Principal],
        [IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat],
        ['query'],
      ),
    'quote' : IDL.Func(
        [IDL.Principal, IDL.Principal, IDL.Nat, IDL.Nat64],
        [Result_3],
        ['query'],
      ),
    'resetParamPrivatePool' : IDL.Func(
        [IDL.Principal, IDL.Nat, IDL.Nat, IDL.Nat],
        [],
        [],
      ),
    'routerPoolInfo' : IDL.Func(
        [],
        [IDL.Vec(IDL.Tuple(IDL.Principal, IDL.Vec(IDL.Principal)))],
        ['query'],
      ),
    'sellShares' : IDL.Func(
        [IDL.Nat, IDL.Principal, IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat64],
        [IDL.Nat, IDL.Nat],
        [],
      ),
    'setFeeToken' : IDL.Func([IDL.Principal], [], []),
    'setTxBucket' : IDL.Func([IDL.Principal], [], []),
    'swapTokenToToken' : IDL.Func(
        [
          IDL.Principal,
          IDL.Principal,
          IDL.Nat,
          IDL.Nat,
          IDL.Vec(IDL.Principal),
          IDL.Nat64,
          IDL.Nat64,
        ],
        [Result_4],
        [],
      ),
    'transferOwnShip' : IDL.Func([IDL.Principal], [], []),
    'updatePool' : IDL.Func([IDL.Principal], [IDL.Nat], []),
    'updatePoolControllers' : IDL.Func([IDL.Principal, IDL.Principal], [], []),
    'withdrawPoolToken' : IDL.Func(
        [IDL.Principal, IDL.Principal, IDL.Principal, IDL.Nat],
        [],
        [],
      ),
    'withdrawSubAccountToken' : IDL.Func(
        [IDL.Principal, IDL.Nat],
        [Result_2],
        [],
      ),
  });
};
export const init = ({ IDL }) => { return [IDL.Principal, IDL.Principal]; };
