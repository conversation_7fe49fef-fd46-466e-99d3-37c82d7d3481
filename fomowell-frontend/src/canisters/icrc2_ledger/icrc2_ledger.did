type Account = record { owner : principal; subaccount : opt vec nat8 };
type Allowance = record { allowance : nat; expires_at : opt nat64 };
type AllowanceArgs = record { account : Account; spender : Account };
type Approve = record {
  fee : opt nat;
  from : Account;
  memo : opt vec nat8;
  created_at_time : opt nat64;
  amount : nat;
  expected_allowance : opt nat;
  expires_at : opt nat64;
  spender : Account;
};
type ApproveArgs = record {
  fee : opt nat;
  memo : opt vec nat8;
  from_subaccount : opt vec nat8;
  created_at_time : opt nat64;
  amount : nat;
  expected_allowance : opt nat;
  expires_at : opt nat64;
  spender : Account;
};
type ApproveError = variant {
  GenericError : record { message : text; error_code : nat };
  TemporarilyUnavailable;
  Duplicate : record { duplicate_of : nat };
  BadFee : record { expected_fee : nat };
  AllowanceChanged : record { current_allowance : nat };
  CreatedInFuture : record { ledger_time : nat64 };
  TooOld;
  Expired : record { ledger_time : nat64 };
  InsufficientFunds : record { balance : nat };
};
type ArchiveInfo = record {
  block_range_end : nat;
  canister_id : principal;
  block_range_start : nat;
};
type ArchiveOptions = record {
  num_blocks_to_archive : nat64;
  max_transactions_per_response : opt nat64;
  trigger_threshold : nat64;
  more_controller_ids : opt vec principal;
  max_message_size_bytes : opt nat64;
  cycles_for_archive_creation : opt nat64;
  node_max_memory_size_bytes : opt nat64;
  controller_id : principal;
};
type ArchivedRange = record {
  callback : func (GetBlocksRequest) -> (BlockRange) query;
  start : nat;
  length : nat;
};
type ArchivedRange_1 = record {
  callback : func (GetBlocksRequest) -> (TransactionRange) query;
  start : nat;
  length : nat;
};
type BlockRange = record { blocks : vec Value };
type Burn = record {
  from : Account;
  memo : opt vec nat8;
  created_at_time : opt nat64;
  amount : nat;
  spender : opt Account;
};
type ChangeFeeCollector = variant { SetTo : Account; Unset };
type DataCertificate = record {
  certificate : opt vec nat8;
  hash_tree : vec nat8;
};
type FeatureFlags = record { icrc2 : bool };
type FeeInfo = record { burn_fee : nat; decimals : nat; transfer_fee : nat };
type GetBlocksRequest = record { start : nat; length : nat };
type GetBlocksResponse = record {
  certificate : opt vec nat8;
  first_index : nat;
  blocks : vec Value;
  chain_length : nat64;
  archived_blocks : vec ArchivedRange;
};
type GetTransactionsResponse = record {
  first_index : nat;
  log_length : nat;
  transactions : vec Transaction;
  archived_transactions : vec ArchivedRange_1;
};
type HoldersBalances = record {
  max_length : nat;
  balances : vec record { Account; nat };
};
type InitArgs = record {
  burn_fee : nat;
  decimals : opt nat8;
  token_symbol : text;
  transfer_fee : nat;
  metadata : vec record { text; MetadataValue };
  minting_account : Account;
  initial_balances : vec record { Account; nat };
  maximum_number_of_accounts : opt nat64;
  accounts_overflow_trim_quantity : opt nat64;
  fee_collector_account : opt Account;
  burn_fee_rate : nat;
  archive_options : ArchiveOptions;
  max_memo_length : opt nat16;
  token_name : text;
  feature_flags : opt FeatureFlags;
  transfer_fee_rate : nat;
};
type LedgerArgument = variant { Upgrade : opt UpgradeArgs; Init : InitArgs };
type MetadataValue = variant {
  Int : int;
  Nat : nat;
  Blob : vec nat8;
  Text : text;
};
type Mint = record {
  to : Account;
  memo : opt vec nat8;
  created_at_time : opt nat64;
  amount : nat;
};
type Result = variant { Ok : nat; Err : TransferError };
type Result_1 = variant { Ok : nat; Err : ApproveError };
type Result_2 = variant { Ok : nat; Err : TransferFromError };
type Result_3 = variant { Ok; Err : text };
type StandardRecord = record { url : text; name : text };
type Transaction = record {
  burn : opt Burn;
  kind : text;
  mint : opt Mint;
  approve : opt Approve;
  timestamp : nat64;
  transfer : opt Transfer;
};
type TransactionRange = record { transactions : vec Transaction };
type Transfer = record {
  to : Account;
  fee : opt nat;
  from : Account;
  memo : opt vec nat8;
  created_at_time : opt nat64;
  amount : nat;
  spender : opt Account;
};
type TransferArg = record {
  to : Account;
  fee : opt nat;
  memo : opt vec nat8;
  from_subaccount : opt vec nat8;
  created_at_time : opt nat64;
  amount : nat;
};
type TransferError = variant {
  GenericError : record { message : text; error_code : nat };
  TemporarilyUnavailable;
  BadBurn : record { min_burn_amount : nat };
  Duplicate : record { duplicate_of : nat };
  BadFee : record { expected_fee : nat };
  CreatedInFuture : record { ledger_time : nat64 };
  TooOld;
  InsufficientFunds : record { balance : nat };
};
type TransferFromArgs = record {
  to : Account;
  fee : opt nat;
  spender_subaccount : opt vec nat8;
  from : Account;
  memo : opt vec nat8;
  created_at_time : opt nat64;
  amount : nat;
};
type TransferFromError = variant {
  GenericError : record { message : text; error_code : nat };
  TemporarilyUnavailable;
  InsufficientAllowance : record { allowance : nat };
  BadBurn : record { min_burn_amount : nat };
  Duplicate : record { duplicate_of : nat };
  BadFee : record { expected_fee : nat };
  CreatedInFuture : record { ledger_time : nat64 };
  TooOld;
  InsufficientFunds : record { balance : nat };
};
type UpgradeArgs = record {
  burn_fee : opt nat;
  token_symbol : opt text;
  transfer_fee : opt nat;
  metadata : opt vec record { text; MetadataValue };
  maximum_number_of_accounts : opt nat64;
  accounts_overflow_trim_quantity : opt nat64;
  change_fee_collector : opt ChangeFeeCollector;
  burn_fee_rate : opt nat;
  max_memo_length : opt nat16;
  token_name : opt text;
  feature_flags : opt FeatureFlags;
  transfer_fee_rate : opt nat;
};
type Value = variant {
  Int : int;
  Map : vec record { text; Value };
  Nat : nat;
  Nat64 : nat64;
  Blob : vec nat8;
  Text : text;
  Array : Vec;
};
type Vec = vec variant {
  Int : int;
  Map : vec record { text; Value };
  Nat : nat;
  Nat64 : nat64;
  Blob : vec nat8;
  Text : text;
  Array : Vec;
};
service : (LedgerArgument) -> {
  archives : () -> (vec ArchiveInfo) query;
  get_blocks : (GetBlocksRequest) -> (GetBlocksResponse) query;
  get_data_certificate : () -> (DataCertificate) query;
  get_transactions : (GetBlocksRequest) -> (GetTransactionsResponse) query;
  icrc1_balance_of : (Account) -> (nat) query;
  icrc1_decimals : () -> (nat8) query;
  icrc1_fee : () -> (nat) query;
  icrc1_metadata : () -> (vec record { text; MetadataValue }) query;
  icrc1_minting_account : () -> (opt Account) query;
  icrc1_name : () -> (text) query;
  icrc1_supported_standards : () -> (vec StandardRecord) query;
  icrc1_symbol : () -> (text) query;
  icrc1_total_supply : () -> (nat) query;
  icrc1_transfer : (TransferArg) -> (Result);
  icrc2_allowance : (AllowanceArgs) -> (Allowance) query;
  icrc2_approve : (ApproveArgs) -> (Result_1);
  icrc2_transfer_from : (TransferFromArgs) -> (Result_2);
  icrc_plus_cycles : () -> (nat) query;
  icrc_plus_fee_info : () -> (FeeInfo) query;
  icrc_plus_holders_balance : (nat64, nat64) -> (HoldersBalances) query;
  icrc_plus_holders_count : () -> (nat) query;
  icrc_plus_set_minting_account : (Account) -> (Result_3);
}