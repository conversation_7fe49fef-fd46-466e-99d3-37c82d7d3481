import type { Principal } from '@dfinity/principal';
import type { ActorMethod } from '@dfinity/agent';
import type { IDL } from '@dfinity/candid';

export interface AddrConfig {
  'router_addr' : Principal,
  'oracle_addr' : Principal,
  'tx_addr' : Principal,
  'backend_addr' : Principal,
  'icpl_addr' : Principal,
}
export interface Context {
  'owner' : Principal,
  'icp_addr' : Principal,
  'last_create_fomo' : CreateFomoSignalVo,
  'last_buy_sell_op' : RecordSignalVo,
  'god_of_wells_idx' : bigint,
  'fomo_canister_template' : Uint8Array | number[],
}
export interface CreateFomoSignalVo {
  'user_name' : string,
  'op_user_pid' : Principal,
  'user_avatar' : string,
  'fomo_idx' : bigint,
  'create_time' : bigint,
  'token_logo' : string,
  'fomo_name' : string,
}
export interface FomoOrderHistory {
  'status' : OrderStatus,
  'refound_time' : [] | [bigint],
  'token_symbol' : string,
  'create_user_pid' : Principal,
  'order_idx' : bigint,
  'fomo_idx' : [] | [bigint],
  'create_time' : bigint,
  'pay_token' : string,
  'amount' : bigint,
}
export interface FomoProject {
  'god_of_wells_time' : [] | [bigint],
  'twitter_link' : string,
  'sneed_dao_lock' : [] | [bigint],
  'market_cap' : bigint,
  'recently_reply_time' : bigint,
  'ticker' : string,
  'img_url' : string,
  'dogmi_dao_lock' : [] | [bigint],
  'name' : string,
  'recently_bump_time' : bigint,
  'create_user_pid' : Principal,
  'description' : string,
  'pool_progress_done_time' : [] | [bigint],
  'telegram_link' : string,
  'website' : string,
  'fomo_idx' : bigint,
  'fomo_pid' : Principal,
  'create_time' : bigint,
  'reply_count' : bigint,
  'token_pid' : Principal,
  'pool_progress' : bigint,
  'pool_pid' : Principal,
  'god_of_wells_progress' : bigint,
}
export interface FomoProjectCreate {
  'twitter_link' : string,
  'sneed_dao_lock' : [] | [bigint],
  'ticker' : string,
  'img_url' : string,
  'dogmi_dao_lock' : [] | [bigint],
  'logo' : string,
  'name' : string,
  'description' : string,
  'telegram_link' : string,
  'website' : string,
  'amount'?: number,
}
export interface FomoProjectCreateWithSwap {
  'twitter_link' : string,
  'sneed_dao_lock' : [] | [bigint],
  'ticker' : string,
  'swap_lp_rate' : bigint,
  'img_url' : string,
  'dogmi_dao_lock' : [] | [bigint],
  'logo' : string,
  'name' : string,
  'description' : string,
  'telegram_link' : string,
  'website' : string,
  'swap_platform' : SupportSwap,
}
export interface FomoProjectInfo {
  'fomo_project' : FomoProject,
  'swap_info' : [] | [ProjectSwap],
}
export interface FomoProjectInfoSearchVo {
  'end' : bigint,
  'start' : bigint,
  'fomo_vec' : Array<FomoProjectInfo>,
}
export interface FomoProjectVo {
  'start_idx' : bigint,
  'end_idx' : bigint,
  'fomo_vec' : Array<FomoProject>,
}
export type OrderStatus = { 'Refunded' : null } |
  { 'Paid' : null } |
  { 'Success' : null } |
  { 'Pending' : null };
export type OrderType = { 'ASC' : null } |
  { 'DESC' : null };
export interface Page { 'limit' : bigint, 'start' : bigint }
export interface PointHistory {
  'idx' : bigint,
  'user_pid' : Principal,
  'time' : bigint,
  'busi_name' : string,
  'op_type' : string,
  'amount' : [] | [bigint],
}
export interface ProjectSwap {
  'receive_quote_amount' : bigint,
  'receive_base_amount' : bigint,
  'lp_amount' : bigint,
  'swap' : SupportSwap,
  'lp_rate' : bigint,
  'total_lp_amount' : bigint,
  'fomo_idx' : bigint,
  'quote_amount' : bigint,
  'base_amount' : bigint,
  'pool_address' : [] | [Principal],
}
export interface RecordSignal {
  'fomo_idx' : bigint,
  'buy_sell_op' : string,
  'icp_amount' : bigint,
  'swap_hash' : bigint,
}
export interface RecordSignalVo {
  'user_name' : string,
  'swap_timestamp' : [] | [bigint],
  'op_user_pid' : Principal,
  'user_avatar' : string,
  'fomo_idx' : bigint,
  'buy_sell_op' : string,
  'icp_amount' : bigint,
  'fomo_ticker' : string,
}
export type Result = { 'Ok' : FomoProject } |
  { 'Err' : string };
export type Result_1 = { 'Ok' : Principal } |
  { 'Err' : string };
export type Result_2 = { 'Ok' : null } |
  { 'Err' : string };
export interface SearchParam {
  'order' : OrderType,
  'sort' : SortType,
  'text' : string,
  'limit' : bigint,
  'start' : bigint,
}
export type SortType = { 'CreationTime' : null } |
  { 'ReplyCount' : null } |
  { 'BumpOrder' : null } |
  { 'LastReply' : null } |
  { 'MarketCap' : null };
export type SupportSwap = { 'ICPEx' : null } |
  { 'SonicSwap' : null } |
  { 'KongSwap' : null } |
  { 'ICPSwap' : null };
export interface UserEditObj {
  'user_name' : [] | [string],
  'avatar' : [] | [string],
}
export interface UserProfile {
  'user_name' : string,
  'user_pid' : Principal,
  'user_points' : [] | [bigint],
  'user_pre_reward_points' : [] | [bigint],
  'last_change_time' : bigint,
  'user_all_spend_points' : [] | [bigint],
  'avatar' : string,
}
export interface _SERVICE {
  'airdrop_points' : ActorMethod<[Principal, bigint, string], undefined>,
  'create_fomo' : ActorMethod<[FomoProjectCreate], Result>,
  'create_fomo_with_swap' : ActorMethod<[FomoProjectCreateWithSwap], Result>,
  'cycles' : ActorMethod<[], bigint>,
  'edit_user' : ActorMethod<[UserEditObj], Result_1>,
  'get_account_id' : ActorMethod<[], string>,
  'get_addr_config' : ActorMethod<[], AddrConfig>,
  'get_buy_or_sell' : ActorMethod<[], RecordSignalVo>,
  'get_dogmi_dao_addr' : ActorMethod<[], Principal>,
  'get_fomo_by_create_user_pid' : ActorMethod<
    [Principal],
    [] | [Array<FomoProject>]
  >,
  'get_fomo_by_fomo_idx' : ActorMethod<[bigint], [] | [FomoProject]>,
  'get_fomo_by_fomo_pid' : ActorMethod<[Principal], [] | [FomoProject]>,
  'get_fomo_by_index' : ActorMethod<[Page], FomoProjectVo>,
  'get_fomo_context' : ActorMethod<[], Context>,
  'get_fomo_info_by_fomo_pid' : ActorMethod<
    [Principal],
    [] | [FomoProjectInfo]
  >,
  'get_fomo_swap_by_fomo_idx' : ActorMethod<[bigint], [] | [ProjectSwap]>,
  'get_god_of_wells' : ActorMethod<[], [] | [FomoProject]>,
  'get_order_history_by_index' : ActorMethod<
    [bigint, bigint],
    Array<[bigint, FomoOrderHistory]>
  >,
  'get_order_history_by_user' : ActorMethod<
    [[] | [Principal]],
    Array<[bigint, FomoOrderHistory]>
  >,
  'get_points_history_by_index' : ActorMethod<
    [bigint, bigint],
    Array<[bigint, PointHistory]>
  >,
  'get_sneed_dao_addr' : ActorMethod<[], Principal>,
  'get_user' : ActorMethod<[Principal], [] | [UserProfile]>,
  'lock_pool' : ActorMethod<[Principal], undefined>,
  'order_refound' : ActorMethod<[bigint, Principal, string], Result_2>,
  'ownership_transfer' : ActorMethod<[Principal, [] | [Principal]], undefined>,
  'query_sell_shares' : ActorMethod<
    [bigint, bigint],
    [bigint, bigint, bigint, bigint]
  >,
  'search_fomos' : ActorMethod<[SearchParam], FomoProjectInfoSearchVo>,
  'set_buy_or_sell' : ActorMethod<[RecordSignal], undefined>,
  'spending_points' : ActorMethod<[Principal, string], undefined>,
  'token_transfer' : ActorMethod<[Principal, bigint, string], Result_2>,
  'topup_points' : ActorMethod<[bigint], Result_2>,
  'update_canister_owner' : ActorMethod<[Principal], Result_2>,
  'update_progress' : ActorMethod<[], undefined>,
}
export declare const idlFactory: IDL.InterfaceFactory;
export declare const init: (args: { IDL: typeof IDL }) => IDL.Type[];