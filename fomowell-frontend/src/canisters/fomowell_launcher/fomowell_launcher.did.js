export const idlFactory = ({ IDL }) => {
  const AddrConfig = IDL.Record({
    'router_addr' : IDL.Principal,
    'oracle_addr' : IDL.Principal,
    'tx_addr' : IDL.Principal,
    'backend_addr' : IDL.Principal,
    'icpl_addr' : IDL.Principal,
  });
  const FomoProjectCreate = IDL.Record({
    'twitter_link' : IDL.Text,
    'sneed_dao_lock' : IDL.Opt(IDL.Nat),
    'ticker' : IDL.Text,
    'img_url' : IDL.Text,
    'dogmi_dao_lock' : IDL.Opt(IDL.Nat),
    'logo' : IDL.Text,
    'name' : IDL.Text,
    'description' : IDL.Text,
    'telegram_link' : IDL.Text,
    'website' : IDL.Text,
  });
  const FomoProject = IDL.Record({
    'god_of_wells_time' : IDL.Opt(IDL.Nat64),
    'twitter_link' : IDL.Text,
    'sneed_dao_lock' : IDL.Opt(IDL.Nat),
    'market_cap' : IDL.Nat,
    'recently_reply_time' : IDL.Nat64,
    'ticker' : IDL.Text,
    'img_url' : IDL.Text,
    'dogmi_dao_lock' : IDL.Opt(IDL.Nat),
    'name' : IDL.Text,
    'recently_bump_time' : IDL.Nat64,
    'create_user_pid' : IDL.Principal,
    'description' : IDL.Text,
    'pool_progress_done_time' : IDL.Opt(IDL.Nat64),
    'telegram_link' : IDL.Text,
    'website' : IDL.Text,
    'fomo_idx' : IDL.Nat64,
    'fomo_pid' : IDL.Principal,
    'create_time' : IDL.Nat64,
    'reply_count' : IDL.Nat64,
    'token_pid' : IDL.Principal,
    'pool_progress' : IDL.Nat,
    'pool_pid' : IDL.Principal,
    'god_of_wells_progress' : IDL.Nat,
  });
  const Result = IDL.Variant({ 'Ok' : FomoProject, 'Err' : IDL.Text });
  const SupportSwap = IDL.Variant({
    'ICPEx' : IDL.Null,
    'SonicSwap' : IDL.Null,
    'KongSwap' : IDL.Null,
    'ICPSwap' : IDL.Null,
  });
  const FomoProjectCreateWithSwap = IDL.Record({
    'twitter_link' : IDL.Text,
    'sneed_dao_lock' : IDL.Opt(IDL.Nat),
    'ticker' : IDL.Text,
    'swap_lp_rate' : IDL.Nat,
    'img_url' : IDL.Text,
    'dogmi_dao_lock' : IDL.Opt(IDL.Nat),
    'logo' : IDL.Text,
    'name' : IDL.Text,
    'description' : IDL.Text,
    'telegram_link' : IDL.Text,
    'website' : IDL.Text,
    'swap_platform' : SupportSwap,
  });
  const UserEditObj = IDL.Record({
    'user_name' : IDL.Opt(IDL.Text),
    'avatar' : IDL.Opt(IDL.Text),
  });
  const Result_1 = IDL.Variant({ 'Ok' : IDL.Principal, 'Err' : IDL.Text });
  const RecordSignalVo = IDL.Record({
    'user_name' : IDL.Text,
    'swap_timestamp' : IDL.Opt(IDL.Nat64),
    'op_user_pid' : IDL.Principal,
    'user_avatar' : IDL.Text,
    'fomo_idx' : IDL.Nat64,
    'buy_sell_op' : IDL.Text,
    'icp_amount' : IDL.Nat,
    'fomo_ticker' : IDL.Text,
  });
  const Page = IDL.Record({ 'limit' : IDL.Nat64, 'start' : IDL.Nat64 });
  const FomoProjectVo = IDL.Record({
    'start_idx' : IDL.Nat64,
    'end_idx' : IDL.Nat64,
    'fomo_vec' : IDL.Vec(FomoProject),
  });
  const CreateFomoSignalVo = IDL.Record({
    'user_name' : IDL.Text,
    'op_user_pid' : IDL.Principal,
    'user_avatar' : IDL.Text,
    'fomo_idx' : IDL.Nat64,
    'create_time' : IDL.Nat64,
    'token_logo' : IDL.Text,
    'fomo_name' : IDL.Text,
  });
  const Context = IDL.Record({
    'owner' : IDL.Principal,
    'icp_addr' : IDL.Principal,
    'last_create_fomo' : CreateFomoSignalVo,
    'last_buy_sell_op' : RecordSignalVo,
    'god_of_wells_idx' : IDL.Nat64,
    'fomo_canister_template' : IDL.Vec(IDL.Nat8),
  });
  const ProjectSwap = IDL.Record({
    'receive_quote_amount' : IDL.Nat,
    'receive_base_amount' : IDL.Nat,
    'lp_amount' : IDL.Nat,
    'swap' : SupportSwap,
    'lp_rate' : IDL.Nat,
    'total_lp_amount' : IDL.Nat,
    'fomo_idx' : IDL.Nat64,
    'quote_amount' : IDL.Nat,
    'base_amount' : IDL.Nat,
    'pool_address' : IDL.Opt(IDL.Principal),
  });
  const FomoProjectInfo = IDL.Record({
    'fomo_project' : FomoProject,
    'swap_info' : IDL.Opt(ProjectSwap),
  });
  const OrderStatus = IDL.Variant({
    'Refunded' : IDL.Null,
    'Paid' : IDL.Null,
    'Success' : IDL.Null,
    'Pending' : IDL.Null,
  });
  const FomoOrderHistory = IDL.Record({
    'status' : OrderStatus,
    'refound_time' : IDL.Opt(IDL.Nat64),
    'token_symbol' : IDL.Text,
    'create_user_pid' : IDL.Principal,
    'order_idx' : IDL.Nat64,
    'fomo_idx' : IDL.Opt(IDL.Nat64),
    'create_time' : IDL.Nat64,
    'pay_token' : IDL.Text,
    'amount' : IDL.Nat,
  });
  const PointHistory = IDL.Record({
    'idx' : IDL.Nat64,
    'user_pid' : IDL.Principal,
    'time' : IDL.Nat64,
    'busi_name' : IDL.Text,
    'op_type' : IDL.Text,
    'amount' : IDL.Opt(IDL.Nat),
  });
  const UserProfile = IDL.Record({
    'user_name' : IDL.Text,
    'user_pid' : IDL.Principal,
    'user_points' : IDL.Opt(IDL.Nat),
    'user_pre_reward_points' : IDL.Opt(IDL.Nat),
    'last_change_time' : IDL.Nat64,
    'user_all_spend_points' : IDL.Opt(IDL.Nat),
    'avatar' : IDL.Text,
  });
  const Result_2 = IDL.Variant({ 'Ok' : IDL.Null, 'Err' : IDL.Text });
  const OrderType = IDL.Variant({ 'ASC' : IDL.Null, 'DESC' : IDL.Null });
  const SortType = IDL.Variant({
    'CreationTime' : IDL.Null,
    'ReplyCount' : IDL.Null,
    'BumpOrder' : IDL.Null,
    'LastReply' : IDL.Null,
    'MarketCap' : IDL.Null,
  });
  const SearchParam = IDL.Record({
    'order' : OrderType,
    'sort' : SortType,
    'text' : IDL.Text,
    'limit' : IDL.Nat64,
    'start' : IDL.Nat64,
  });
  const FomoProjectInfoSearchVo = IDL.Record({
    'end' : IDL.Nat64,
    'start' : IDL.Nat64,
    'fomo_vec' : IDL.Vec(FomoProjectInfo),
  });
  const RecordSignal = IDL.Record({
    'fomo_idx' : IDL.Nat64,
    'buy_sell_op' : IDL.Text,
    'icp_amount' : IDL.Nat,
    'swap_hash' : IDL.Nat64,
  });
  return IDL.Service({
    'airdrop_points' : IDL.Func([IDL.Principal, IDL.Nat, IDL.Text], [], []),
    'create_fomo' : IDL.Func([FomoProjectCreate], [Result], []),
    'create_fomo_with_swap' : IDL.Func(
        [FomoProjectCreateWithSwap],
        [Result],
        [],
      ),
    'cycles' : IDL.Func([], [IDL.Nat64], ['query']),
    'edit_user' : IDL.Func([UserEditObj], [Result_1], []),
    'get_account_id' : IDL.Func([], [IDL.Text], ['query']),
    'get_addr_config' : IDL.Func([], [AddrConfig], ['query']),
    'get_buy_or_sell' : IDL.Func([], [RecordSignalVo], ['query']),
    'get_dogmi_dao_addr' : IDL.Func([], [IDL.Principal], []),
    'get_fomo_by_create_user_pid' : IDL.Func(
        [IDL.Principal],
        [IDL.Opt(IDL.Vec(FomoProject))],
        ['query'],
      ),
    'get_fomo_by_fomo_idx' : IDL.Func(
        [IDL.Nat64],
        [IDL.Opt(FomoProject)],
        ['query'],
      ),
    'get_fomo_by_fomo_pid' : IDL.Func(
        [IDL.Principal],
        [IDL.Opt(FomoProject)],
        ['query'],
      ),
    'get_fomo_by_index' : IDL.Func([Page], [FomoProjectVo], ['query']),
    'get_fomo_context' : IDL.Func([], [Context], ['query']),
    'get_fomo_info_by_fomo_pid' : IDL.Func(
        [IDL.Principal],
        [IDL.Opt(FomoProjectInfo)],
        ['query'],
      ),
    'get_fomo_swap_by_fomo_idx' : IDL.Func(
        [IDL.Nat64],
        [IDL.Opt(ProjectSwap)],
        ['query'],
      ),
    'get_god_of_wells' : IDL.Func([], [IDL.Opt(FomoProject)], ['query']),
    'get_order_history_by_index' : IDL.Func(
        [IDL.Nat64, IDL.Nat64],
        [IDL.Vec(IDL.Tuple(IDL.Nat64, FomoOrderHistory))],
        ['query'],
      ),
    'get_order_history_by_user' : IDL.Func(
        [IDL.Opt(IDL.Principal)],
        [IDL.Vec(IDL.Tuple(IDL.Nat64, FomoOrderHistory))],
        ['query'],
      ),
    'get_points_history_by_index' : IDL.Func(
        [IDL.Nat64, IDL.Nat64],
        [IDL.Vec(IDL.Tuple(IDL.Nat64, PointHistory))],
        ['query'],
      ),
    'get_sneed_dao_addr' : IDL.Func([], [IDL.Principal], []),
    'get_user' : IDL.Func([IDL.Principal], [IDL.Opt(UserProfile)], ['query']),
    'lock_pool' : IDL.Func([IDL.Principal], [], []),
    'order_refound' : IDL.Func(
        [IDL.Nat64, IDL.Principal, IDL.Text],
        [Result_2],
        [],
      ),
    'ownership_transfer' : IDL.Func(
        [IDL.Principal, IDL.Opt(IDL.Principal)],
        [],
        [],
      ),
    'query_sell_shares' : IDL.Func(
        [IDL.Nat64, IDL.Nat],
        [IDL.Nat, IDL.Nat, IDL.Nat, IDL.Nat],
        [],
      ),
    'search_fomos' : IDL.Func(
        [SearchParam],
        [FomoProjectInfoSearchVo],
        ['query'],
      ),
    'set_buy_or_sell' : IDL.Func([RecordSignal], [], []),
    'spending_points' : IDL.Func([IDL.Principal, IDL.Text], [], []),
    'token_transfer' : IDL.Func(
        [IDL.Principal, IDL.Nat, IDL.Text],
        [Result_2],
        [],
      ),
    'topup_points' : IDL.Func([IDL.Nat], [Result_2], []),
    'update_canister_owner' : IDL.Func([IDL.Principal], [Result_2], []),
    'update_progress' : IDL.Func([], [], []),
  });
};
export const init = ({ IDL }) => {
  const AddrConfig = IDL.Record({
    'router_addr' : IDL.Principal,
    'oracle_addr' : IDL.Principal,
    'tx_addr' : IDL.Principal,
    'backend_addr' : IDL.Principal,
    'icpl_addr' : IDL.Principal,
  });
  return [AddrConfig, IDL.Principal];
};