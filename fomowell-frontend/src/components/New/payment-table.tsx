import type { FomoOrderHistoryData } from "@/api/fomowell_launcher";
import {
	FomoOrderHistory,
	OrderStatus,
} from "@/canisters/fomowell_launcher/fomowell_launcher.did";
import { useGetOrderHistory, useOrderRefound } from "@/hooks/api";
import {
	useReactTable,
	getCoreRowModel,
	flexRender,
	createColumnHelper,
} from "@tanstack/react-table";
import { useEffect, useState } from "react";
import dayjs from "dayjs";
import { Box, Modal } from "@mui/material";
import { isValidPrincipal } from "@/utils/common";
import Message from "../Snackbar/message";
import { Principal } from "@dfinity/principal";
import { LoadingButton } from "@mui/lab";
import { CloseSvg } from "./sns-notice-modal";
import { useParams } from "react-router-dom";
const SuccessSvg = () => (
	// biome-ignore lint/a11y/noSvgWithoutTitle: <explanation>
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width="13"
		height="13"
		viewBox="0 0 13 13"
		fill="none"
	>
		<path
			fillRule="evenodd"
			clipRule="evenodd"
			d="M9.0963 1.25681C8.36829 0.955228 7.588 0.800003 6.79999 0.800003C5.61334 0.800003 4.45334 1.15187 3.46666 1.81112C2.47999 2.47037 1.71095 3.40739 1.2568 4.50369C0.802649 5.59999 0.683775 6.80634 0.915211 7.9702C1.14665 9.13406 1.718 10.2032 2.55702 11.0423C3.39605 11.8815 4.46506 12.453 5.62888 12.6846C6.79271 12.9162 7.99908 12.7975 9.09545 12.3435C10.1918 11.8896 11.129 11.1207 11.7884 10.1341C12.4478 9.14752 12.7998 7.98757 12.8 6.80092C12.8001 6.01292 12.645 5.2326 12.3435 4.50454C12.0421 3.77648 11.6001 3.11494 11.043 2.55769C10.4858 2.00044 9.82432 1.5584 9.0963 1.25681ZM4.85692 2.10963C5.47294 1.85443 6.1332 1.72308 6.79999 1.72308C7.80407 1.72308 8.7856 2.02081 9.62048 2.57863C10.4554 3.13645 11.1061 3.9293 11.4904 4.85693C11.8747 5.78457 11.9752 6.80532 11.7794 7.79012C11.5836 8.77492 11.1002 9.67954 10.3902 10.3896C9.68031 11.0997 8.77578 11.5833 7.79101 11.7793C6.80625 11.9753 5.78548 11.8748 4.85777 11.4907C3.93007 11.1066 3.1371 10.456 2.57913 9.62126C2.02116 8.78649 1.72325 7.80501 1.72307 6.80092C1.72295 6.13414 1.85417 5.47385 2.10926 4.85779C2.36434 4.24172 2.73829 3.68194 3.20974 3.2104C3.68118 2.73887 4.2409 2.36482 4.85692 2.10963ZM6.22912 9.3633C6.28951 9.32716 6.34106 9.27799 6.38002 9.21939L10.2016 5.31754C10.3862 5.12831 10.3862 4.8237 10.2016 4.63446C10.1575 4.58947 10.105 4.55372 10.047 4.52932C9.98896 4.50491 9.92665 4.49234 9.86371 4.49234C9.80077 4.49234 9.73846 4.50491 9.68045 4.52932C9.62243 4.55372 9.56988 4.58947 9.52586 4.63446L5.98033 8.25754L4.42033 6.67354C4.37633 6.62869 4.32383 6.59306 4.2659 6.56874C4.20797 6.54442 4.14577 6.53189 4.08294 6.53189C4.02011 6.53189 3.95791 6.54442 3.89998 6.56874C3.84205 6.59306 3.78955 6.62869 3.74556 6.67354C3.65631 6.76464 3.60632 6.88709 3.60632 7.01462C3.60632 7.14215 3.65631 7.2646 3.74556 7.35569L5.64894 9.28862C5.69821 9.33887 5.75807 9.37749 5.82417 9.40165C5.89026 9.42581 5.96092 9.4349 6.03098 9.42827C6.10104 9.42163 6.16874 9.39944 6.22912 9.3633Z"
			fill="#00DC60"
		/>
	</svg>
);

const FailedSvg = () => (
	// biome-ignore lint/a11y/noSvgWithoutTitle: <explanation>
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width="13"
		height="13"
		viewBox="0 0 13 13"
		fill="none"
	>
		<path
			fillRule="evenodd"
			clipRule="evenodd"
			d="M9.49527 1.52922C10.8436 2.1585 11.91 3.26634 12.4875 4.63768C12.799 5.37443 12.9589 6.16635 12.9578 6.96624C12.9578 8.4542 12.4049 9.88908 11.4064 10.9923C10.408 12.0956 9.03523 12.7884 7.55465 12.9365C6.07407 13.0845 4.5913 12.6771 3.39421 11.7933C2.19712 10.9096 1.37112 9.61253 1.07655 8.15402C0.78199 6.6955 1.03988 5.17956 1.80017 3.9005C2.56045 2.62144 3.76889 1.6705 5.19087 1.23231C6.61285 0.794125 8.14692 0.899941 9.49527 1.52922ZM9.19015 11.7492C10.3767 11.1956 11.3153 10.2207 11.8235 9.01399C12.0974 8.36608 12.2382 7.66968 12.2374 6.96624C12.2378 5.65683 11.7516 4.39401 10.8732 3.42293C9.99484 2.45185 8.78696 1.84182 7.48408 1.71127C6.1812 1.58071 4.87629 1.93896 3.8227 2.71644C2.76911 3.49393 2.04202 4.63518 1.78258 5.91862C1.52315 7.20207 1.74989 8.53613 2.41878 9.66179C3.08767 10.7875 4.15098 11.6244 5.40228 12.0102C6.65357 12.3959 8.00356 12.3029 9.19015 11.7492ZM8.8452 4.74722C8.9408 4.74722 9.03249 4.78519 9.1001 4.85279C9.16769 4.9204 9.20567 5.01209 9.20567 5.10769C9.20567 5.20329 9.16769 5.29498 9.1001 5.36259L7.48563 6.97705L9.1001 8.59151C9.16614 8.65944 9.20279 8.75063 9.20213 8.84537C9.20146 8.9401 9.16353 9.03077 9.09654 9.09776C9.02955 9.16475 8.93888 9.20268 8.84415 9.20335C8.74941 9.20401 8.65822 9.16736 8.59029 9.10132L6.97583 7.48685L5.36137 9.10132C5.32807 9.13557 5.28829 9.16286 5.24436 9.1816C5.20042 9.20035 5.15319 9.21017 5.10542 9.21051C5.05765 9.21084 5.01029 9.20168 4.96609 9.18356C4.9219 9.16543 4.88174 9.1387 4.84796 9.10492C4.81418 9.07114 4.78746 9.03099 4.76933 8.98679C4.7512 8.94259 4.74204 8.89523 4.74238 8.84746C4.74271 8.7997 4.75254 8.75247 4.77128 8.70853C4.79003 8.66459 4.81732 8.62482 4.85157 8.59151L6.46603 6.97705L4.85157 5.36259C4.78553 5.29467 4.74887 5.20347 4.74954 5.10874C4.7502 5.014 4.78813 4.92333 4.85512 4.85634C4.92211 4.78935 5.01278 4.75142 5.10752 4.75076C5.20225 4.75009 5.29345 4.78674 5.36137 4.85279L6.97583 6.46725L8.59029 4.85279C8.6579 4.78519 8.74959 4.74722 8.8452 4.74722Z"
			fill="#FF5B4C"
		/>
	</svg>
);
const PendingSvg = () => (
	// biome-ignore lint/a11y/noSvgWithoutTitle: <explanation>
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width="13"
		height="13"
		viewBox="0 0 13 13"
		fill="none"
	>
		<path
			fillRule="evenodd"
			clipRule="evenodd"
			d="M9.09609 1.25673C8.36813 0.955198 7.58792 0.800003 6.79999 0.800003C5.20869 0.800003 3.68257 1.43214 2.55735 2.55736C1.43213 3.68258 0.799988 5.2087 0.799988 6.8C0.799988 8.3913 1.43213 9.91743 2.55735 11.0426C3.68257 12.1679 5.20869 12.8 6.79999 12.8C7.58792 12.8 8.36813 12.6448 9.09609 12.3433C9.82404 12.0418 10.4855 11.5998 11.0426 11.0426C11.5998 10.4855 12.0417 9.82406 12.3433 9.0961C12.6448 8.36815 12.8 7.58793 12.8 6.8C12.8 6.01207 12.6448 5.23186 12.3433 4.5039C12.0417 3.77595 11.5998 3.11451 11.0426 2.55736C10.4855 2.00021 9.82404 1.55825 9.09609 1.25673ZM3.21006 3.21008C4.16217 2.25797 5.4535 1.72308 6.79999 1.72308C8.14647 1.72308 9.43781 2.25797 10.3899 3.21008C11.342 4.16218 11.8769 5.45352 11.8769 6.8C11.8769 8.14649 11.342 9.43782 10.3899 10.3899C9.43781 11.342 8.14647 11.8769 6.79999 11.8769C5.4535 11.8769 4.16217 11.342 3.21006 10.3899C2.25795 9.43782 1.72306 8.14649 1.72306 6.8C1.72306 5.45352 2.25795 4.16218 3.21006 3.21008ZM8.63803 9.74567C8.69426 9.7223 8.74533 9.68807 8.7883 9.64492C8.87536 9.55751 8.92448 9.43932 8.925 9.31595C8.92552 9.19258 8.8774 9.07397 8.79107 8.98584C8.79107 8.98584 7.24953 7.45723 7.24953 7.44338V4.16646C7.24953 3.908 7.04276 3.69846 6.78707 3.69846C6.5323 3.69846 6.3246 3.908 6.3246 4.16646C6.3246 4.16646 6.3846 7.18307 6.3846 7.4443C6.3846 7.46487 6.38453 7.48487 6.38447 7.50433C6.38368 7.73129 6.38315 7.88508 6.49537 7.99815L8.13291 9.64492C8.17588 9.68807 8.22695 9.7223 8.28318 9.74567C8.33942 9.76903 8.39971 9.78106 8.4606 9.78106C8.5215 9.78106 8.58179 9.76903 8.63803 9.74567Z"
			fill="#CCCCCC"
		/>
	</svg>
);
const mockData: FomoOrderHistoryData[] = [
	{
		orderIdx: 1n,
		amount: 100n,
		tokenSymbol: "USDT",
		createTime: 1734598917000000000n,
		status: "Success",
		refoundTime: null,
		createUserPid: "",
		fomoIdx: 2n,
		payToken: "",
		refundStatus: "Pending",
	},
	{
		orderIdx: 1n,
		amount: 100n,
		tokenSymbol: "USDT",
		createTime: 1734598917000000000n,
		status: "Paid",
		refoundTime: null,
		createUserPid: "",
		fomoIdx: 2n,
		payToken: "",
		refundStatus: "Paid",
	},
	{
		orderIdx: 1n,
		amount: 100n,
		tokenSymbol: "USDT",
		createTime: 1734598917000000000n,
		status: "Refunded",
		refoundTime: null,
		createUserPid: "",
		fomoIdx: 2n,
		payToken: "",
		refundStatus: "Refunded",
	},
	{
		orderIdx: 1n,
		amount: 100n,
		tokenSymbol: "USDT",
		createTime: 1734598917000000000n,
		status: "Pending",
		refoundTime: null,
		createUserPid: "",
		fomoIdx: 2n,
		payToken: "",
		refundStatus: "Pending",
	},
];
export const PaymentTable = () => {
	const columnHelper = createColumnHelper<FomoOrderHistoryData>();
	const { user_pid } = useParams<{ user_pid: string }>();
	const { data, refetch, error } = useGetOrderHistory(user_pid);
	const [openModal, setOpenModal] = useState(false);
	const [selectedOrder, setSelectedOrder] =
		useState<FomoOrderHistoryData | null>(null);
	const [refundAddress, setRefundAddress] = useState("");
	const [memo, setMemo] = useState("");
	const { mutateAsync: orderRefound, isPending: isRefoundLoading } =
		useOrderRefound();
	const onConfirmRefound = async () => {
		if (!isValidPrincipal(refundAddress)) {
			Message.error("Invalid principal ID");
			return;
		}
		try {
			await orderRefound({
				orderIdx: selectedOrder?.orderIdx || 0n,
				userPid: Principal.fromText(refundAddress),
				memo,
			});
			refetch();
			setOpenModal(false);
			Message.success("Refund successfully");
		} catch (error) {
			Message.error(`Refund failed${error}`);
		}
	};
	const confirmDisabled =
		!isValidPrincipal(refundAddress) ||
		selectedOrder?.status !== "Paid" ||
		isRefoundLoading;
	const columns = [
		columnHelper.accessor("orderIdx", {
			header: "Order",
			cell: (info) => info.getValue().toString(),
		}),
		columnHelper.accessor("fomoIdx", {
			header: "Item",
			cell: (info) => info.getValue()?.toString() || "-",
		}),
		columnHelper.accessor("tokenSymbol", {
			header: "Token",
			cell: (info) => info.getValue(),
		}),
		columnHelper.accessor("createTime", {
			header: "Payment Time",
			cell: (info) => {
				const timestamp = Number(info.getValue() / 1_000_000n);
				return dayjs(timestamp).format("MM/DD/YY HH:mm:ss");
			},
		}),
		columnHelper.accessor("status", {
			header: "Payment Status",
			cell: (info) => (
				<div
					className={"rounded justify-start text-sm gap-x-1 flex items-center"}
				>
					{info.getValue() === "Success" ? (
						<>
							<SuccessSvg />
							<span className="text-sm text-[#00DC60]">Success</span>
						</>
					) : (
						""
					)}
					{info.getValue() === "Paid" ? (
						<>
							<FailedSvg />
							<span className="text-sm text-[#FF5B4C]">Failed</span>
						</>
					) : (
						""
					)}
				</div>
			),
		}),
		columnHelper.accessor("refoundTime", {
			header: "Refund Time",
			cell: (info) => {
				const value = info.getValue();
				const timestamp = value ? Number(value / 1_000_000n) : null;
				return timestamp ? dayjs(timestamp).format("MM/DD/YY HH:mm:ss") : "-";
			},
		}),
		columnHelper.accessor("refundStatus", {
			header: "Refund Status",
			cell: (info) => (
				<div className={"rounded text-sm gap-x-1 flex items-center"}>
					{info.getValue() === "Refunded" ? (
						<>
							<SuccessSvg />
							<span className="text-sm text-[#00DC60]">Success</span>
						</>
					) : (
						""
					)}
					{info.getValue() === "Pending" ? (
						<>
							<PendingSvg />
							<span className="text-sm text-[#CCCCCC]">Pending</span>
						</>
					) : (
						""
					)}
				</div>
			),
		}),
		columnHelper.display({
			id: "actions",
			header: "Operation",
			cell: (info) => {
				const status = info.row.original.status;
				const createTime = info.row.original.createTime;
				// 将纳秒转换为毫秒
				const createTimeMs = Number(createTime / 1_000_000n);
				const now = Date.now();
				const diffMinutes = dayjs(now).diff(createTimeMs, "minute");

				const canRefund = status === "Paid" && diffMinutes >= 20;
				// 只有在支付成功且未退款的情况下显示退款按钮
				if (status === "Paid") {
					return (
						<button
							disabled={!canRefund}
							type="button"
							onClick={() => {
								setSelectedOrder(info.row.original);
								setOpenModal(true);
							}}
							className="text-[#F9B000] disabled:text-[#F9B000]/50 hover:text-[#F9B000]"
						>
							Refund
						</button>
					);
				}
			},
		}),
	];

	const table = useReactTable({
		data: data?.data || [],
		columns,
		getCoreRowModel: getCoreRowModel(),
	});
	return (
		<div className="w-[800px] overflow-x-auto">
			{data?.data && data?.data.length > 0 && (
				<table className="min-w-full table-fixed divide-y divide-gray-700">
					<thead className="bg-[#222636]">
						{table.getHeaderGroups().map((headerGroup) => (
							<tr key={headerGroup.id}>
								{headerGroup.headers.map((header) => (
									<th
										key={header.id}
										className="px-2 py-2 text-left text-[12px] font-semibold text-[#939CC1] font-['PingFang_HK']"
									>
										{flexRender(
											header.column.columnDef.header,
											header.getContext(),
										)}
									</th>
								))}
							</tr>
						))}
					</thead>
					<tbody className="bg-transparent divide-y divide-gray-700">
						{table.getRowModel().rows.map((row) => (
							<tr key={row.id}>
								{row.getVisibleCells().map((cell) => (
									<td
										key={cell.id}
										className="px-2 h-10 text-[12px] text-white font-['PingFang_HK'] font-normal"
									>
										{flexRender(cell.column.columnDef.cell, cell.getContext())}
									</td>
								))}
							</tr>
						))}
					</tbody>
				</table>
			)}
			<Modal
				open={openModal}
				onClose={() => setOpenModal(false)}
				aria-labelledby="refund-modal"
				aria-describedby="refund-confirmation-modal"
			>
				<Box
					className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 
    w-[500px] bg-[#272938] rounded-2xl border border-[#31333D] p-[30px]"
				>
					<h2 className="text-lg text-center font-semibold text-white mb-4">
						Refund
					</h2>
					{/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
					<div
						className="absolute top-5 cursor-pointer right-5 flex justify-end"
						onClick={() => setOpenModal(false)}
					>
						<CloseSvg />
					</div>
					<div>
						<div className="text-[14px] text-[#CCC] font-['PingFang_HK'] font-normal">
							Refund Address
						</div>
						<div className="mt-2">
							<input
								value={refundAddress}
								onChange={(e) => setRefundAddress(e.target.value)}
								placeholder="Your Principal ID"
								className="w-[440px] h-[44px] flex-shrink-0 rounded-[8px] border border-[#31333D] bg-[#202230] text-white px-2 py-1 placeholder:text-[#999] placeholder:font-['PingFang_HK'] placeholder:text-[14px] placeholder:font-normal focus:outline-none focus:border-[#8046FF]"
							/>
						</div>
					</div>
					<div className="mt-4">
						<div className="text-[14px] text-[#CCC] font-['PingFang_HK'] font-normal">
							Memo
						</div>
						<div className="mt-2">
							<textarea
								value={memo}
								onChange={(e) => setMemo(e.target.value)}
								placeholder="Please state your memo"
								className="w-full h-[143px] rounded-md border border-[#31333D] bg-[#202230] text-white px-2 py-1 placeholder:text-[#999] placeholder:font-['PingFang_HK'] placeholder:text-[14px] placeholder:font-normal focus:outline-none focus:border-[#8046FF]"
							/>
						</div>
					</div>
					<div className="flex justify-end mt-[22px] gap-4">
						{/* biome-ignore lint/a11y/useButtonType: <explanation> */}
						<LoadingButton
							loading={isRefoundLoading}
							className="px-4 py-2 w-full disabled:opacity-50 !disabled:cursor-not-allowed !h-[44px]  !text-white !capitalize  !rounded-md !transition-colors !duration-200"
							onClick={onConfirmRefound}
							disabled={confirmDisabled}
							style={{
								background: "linear-gradient(90deg, #7139FF 0%, #A15FFF 100%)",
							}}
						>
							Confirm
						</LoadingButton>
					</div>
				</Box>
			</Modal>
		</div>
	);
};
