import useModalStore from "@/store/modal";
import { Box, Modal } from "@mui/material";
import { useState } from "react";

export const CloseSvg = () => (
	// biome-ignore lint/a11y/noSvgWithoutTitle: <explanation>
	<svg
		xmlns="http://www.w3.org/2000/svg"
		width="12"
		height="12"
		viewBox="0 0 12 12"
		fill="none"
	>
		<path
			d="M11.7162 0.283832C11.5344 0.102096 11.2879 0 11.0308 0C10.7738 0 10.5273 0.102096 10.3455 0.283832L6.00488 4.62447L1.66425 0.283832C1.48162 0.106269 1.23645 0.0077263 0.981738 0.00951505C0.72703 0.0113038 0.483263 0.113281 0.303152 0.293391C0.123042 0.473501 0.0210652 0.717269 0.0192764 0.971977C0.0174877 1.22669 0.116031 1.47186 0.293594 1.65448L4.63423 5.99512L0.293594 10.3358C0.20151 10.4253 0.128138 10.5322 0.0777406 10.6504C0.0273434 10.7685 0.000925847 10.8955 2.39033e-05 11.0239C-0.000878041 11.1523 0.0237532 11.2797 0.0724862 11.3985C0.121219 11.5173 0.193083 11.6253 0.2839 11.7161C0.374717 11.8069 0.482677 11.8788 0.601508 11.9275C0.720338 11.9762 0.847669 12.0009 0.9761 12C1.10453 11.9991 1.2315 11.9727 1.34964 11.9223C1.46777 11.8719 1.57471 11.7985 1.66425 11.7064L6.00488 7.36577L10.3455 11.7064C10.5281 11.884 10.7733 11.9825 11.028 11.9807C11.2827 11.9789 11.5265 11.877 11.7066 11.6968C11.8867 11.5167 11.9887 11.273 11.9905 11.0183C11.9923 10.7636 11.8937 10.5184 11.7162 10.3358L7.37553 5.99512L11.7162 1.65448C11.8979 1.47271 12 1.2262 12 0.969158C12 0.712119 11.8979 0.465604 11.7162 0.283832Z"
			fill="#CCCCCC"
		/>
	</svg>
);
export default function SnsNoticeModal() {
	const [openModal, setOpenModal] = useState(true);
	const { snsNoticeModalShowed, setSnsNoticeModalShowed } = useModalStore();
	const onClose = () => {
		setSnsNoticeModalShowed(true);
		setOpenModal(false);
	};
	return (
		<Modal
			open={openModal && !snsNoticeModalShowed}
			onClose={onClose}
			aria-labelledby="sns-notice-modal"
			aria-describedby="sns-launch-announcement-modal"
		>
			<Box
				className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 
				md:w-[600px] w-[90%] bg-[#272938] rounded-2xl border border-[#31333D] p-[30px]"
			>
				<h2 className="text-2xl text-center font-bold text-white mb-6">
					Notice: Fomowell is Launching on the SNS!
				</h2>
				{/* Close button */}
				{/* biome-ignore lint/a11y/useKeyWithClickEvents: <explanation> */}
				<div
					className="absolute top-5 cursor-pointer right-5 flex justify-end"
					onClick={onClose}
				>
					<CloseSvg />
				</div>

				{/* Content */}
				<div className="text-white space-y-4">
					<p className="text-base leading-relaxed">
						We are thrilled to announce that Fomowell will soon launch on SNS,
						marking a key milestone in the evolution of the meme world. Your
						continued support is greatly appreciated as we move forward.
					</p>

					<div className="mt-6">
						<h3 className="text-lg font-semibold mb-2">Stay Connected:</h3>
						<p className="text-base leading-relaxed mb-4">
							Make sure to follow us on Twitter and join the community and keep
							an eye out for updates as we unveil more details about this
							launch. Together, let&apos;s redefine the future of memes on the
							blockchain!
						</p>
					</div>

					{/* Links section */}
					<div className="space-y-2 mt-6">
						<a
							href="https://fomowell.com"
							target="_blank"
							rel="noopener noreferrer"
							className="flex items-center text-blue-400 hover:text-blue-300"
						>
							🏠 Website: fomowell.com
						</a>
						<a
							href="https://x.com/fomowellcom"
							target="_blank"
							rel="noopener noreferrer"
							className="flex items-center text-blue-400 hover:text-blue-300"
						>
							📖 X: @fomowellcom
						</a>
						<a
							href="https://t.me/fomowell"
							target="_blank"
							rel="noopener noreferrer"
							className="flex items-center text-blue-400 hover:text-blue-300"
						>
							✈️ TG: @fomowell
						</a>
					</div>

					<div className="mt-6">
						<p className="text-base">
							Thank you for being a part of this journey.
						</p>
						<p className="text-base mt-2">Sincerely,</p>
						<p className="text-base font-semibold">The Fomowell Team</p>
					</div>
				</div>
			</Box>
		</Modal>
	);
}
