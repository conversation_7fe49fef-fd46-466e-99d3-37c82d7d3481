import { getCanisters } from '../../config/env';
import { canisterId as platTokenId } from '@/canisters/icrc2_ledger';

// dfinity environment
export const isProduction: boolean = process.env.DFX_NETWORK === 'ic';

export const host: string = isProduction ? 'https://icp-api.io' : 'http://localhost:8000';

export const PLAT_TOKEN_CANISTER_ID: string = platTokenId;

export const whitelist: any = getCanisters();

// max count and max token
export const MAX_COUNT_TO_SHOOT = 2;
export const MAX_TOKEN_TO_SHOOT = 1000000000;

// base test chain
// export const BASE_UR_CONTRACT_ADDRESS = '0x36619BAC0Fbc8C500D55EA08A8051d2e6268d9C8';

export const BASE_UR_CONTRACT_ADDRESS = '0x0252de67ae6D3e43770bBccabF904B1b47cb2F89';
