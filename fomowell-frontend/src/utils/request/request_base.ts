import axios, { AxiosRequestConfig, AxiosResponse, AxiosError } from 'axios';
export const API_URL = 'https://base.fomowell.com';

interface ApiResponse<T = unknown> {
    data: T;
    status: number;
    statusText?: string;
}

export const requestApi = async <T = unknown>(
    url: string,
    method = 'get',
    data?: Record<string, unknown>,
): Promise<T> => {
    const config: AxiosRequestConfig = {
        baseURL: API_URL,
        method,
        url,
        timeout: 300000,
        headers: {
            'Content-Type': 'application/json',
        },
    };

    if (method === 'get') {
        config.params = data;
    } else {
        config.data = data;
    }

    axios.interceptors.request.use(
        (config) => config,
        (error: AxiosError) => Promise.resolve(error.response || error),
    );

    axios.interceptors.response.use(
        (response) => response,
        (error: AxiosError) => Promise.resolve(error.response || error),
    );

    const result = await axios({
        baseURL: API_URL,
        timeout: 300000,
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Content-Type': 'application/json',
        },
        ...config,
    });

    return checkStatus<T>(result);
};

const checkStatus = async <T>(response: AxiosResponse | ApiResponse): Promise<T> => {
    if (response.status === 200) {
        return (response as AxiosResponse).data;
    }

    return {} as T;
};

export const $api = {
    post<T = unknown>(url: string, data?: Record<string, unknown>): Promise<T> {
        return requestApi<T>(url, 'POST', data);
    },
    get<T = unknown>(url: string, data?: Record<string, unknown>): Promise<T> {
        return requestApi<T>(url, 'GET', data);
    },
};
