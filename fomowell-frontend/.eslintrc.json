{"env": {"browser": true, "es2021": true}, "extends": ["eslint:recommended", "plugin:react/recommended", "plugin:@typescript-eslint/recommended"], "overrides": [], "parser": "@typescript-eslint/parser", "parserOptions": {"ecmaVersion": "latest", "sourceType": "module"}, "plugins": ["react", "@typescript-eslint"], "rules": {"react/react-in-jsx-scope": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-unused-vars": "warn"}}